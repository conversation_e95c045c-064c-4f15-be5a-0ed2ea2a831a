package com.example.aicamera

/**
 * Configuration class for API endpoints and settings
 */
object ApiConfig {
    
    // API Provider Types
    enum class ApiProvider {
        LOCAL_SERVER,
        HUGGING_FACE
    }
    
    // Current API provider - change this to switch between local and cloud
    var currentProvider: ApiProvider = ApiProvider.HUGGING_FACE
    
    // Local Server Configuration
    object LocalServer {
        const val BASE_URL = "http://*************:8000/"
        const val ENDPOINT = "process-image"
    }
    
    // Hugging Face Configuration
    object HuggingFace {
        const val BASE_URL = "https://api-inference.huggingface.co/"
        
        // Available models for different use cases
        const val IMAGE_CAPTIONING_MODEL = "Salesforce/blip-image-captioning-base"
        const val DOCUMENT_OCR_MODEL = "microsoft/trocr-base-printed"
        const val GENERAL_VIT_MODEL = "nlpconnect/vit-gpt2-image-captioning"
        const val GIT_MODEL = "microsoft/git-base-coco"
        
        // Current model being used
        var currentModel: String = IMAGE_CAPTIONING_MODEL
        
        // API Token - should be set from secure storage or user input
        var apiToken: String = ""
        
        // Rate limiting and size constraints
        const val MAX_IMAGE_SIZE_MB = 5
        const val MAX_REQUESTS_PER_HOUR = 1000 // Free tier limit
        const val RECOMMENDED_IMAGE_SIZE = 1024 // pixels
    }
    
    // Network Configuration
    object Network {
        const val CONNECT_TIMEOUT_SECONDS = 30L
        const val READ_TIMEOUT_SECONDS = 60L // Longer for cloud processing
        const val WRITE_TIMEOUT_SECONDS = 60L
    }
    
    /**
     * Get the current base URL based on selected provider
     */
    fun getBaseUrl(): String {
        return when (currentProvider) {
            ApiProvider.LOCAL_SERVER -> LocalServer.BASE_URL
            ApiProvider.HUGGING_FACE -> HuggingFace.BASE_URL
        }
    }
    
    /**
     * Get the current endpoint based on selected provider
     */
    fun getEndpoint(): String {
        return when (currentProvider) {
            ApiProvider.LOCAL_SERVER -> LocalServer.ENDPOINT
            ApiProvider.HUGGING_FACE -> "models/${HuggingFace.currentModel}"
        }
    }
    
    /**
     * Check if API token is required and available
     */
    fun isApiTokenRequired(): Boolean {
        return currentProvider == ApiProvider.HUGGING_FACE
    }
    
    /**
     * Validate current configuration
     */
    fun isConfigurationValid(): Boolean {
        return when (currentProvider) {
            ApiProvider.LOCAL_SERVER -> true // No token required
            ApiProvider.HUGGING_FACE -> HuggingFace.apiToken.isNotBlank()
        }
    }
}
