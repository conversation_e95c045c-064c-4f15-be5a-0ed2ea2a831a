<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/black"
    tools:context=".CropActivity">

    <!-- Toolbar -->
    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar"
        android:layout_width="0dp"
        android:layout_height="?attr/actionBarSize"
        android:background="@color/black"
        android:theme="@style/ThemeOverlay.AppCompat.Dark.ActionBar"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:title="@string/crop_image"
        app:titleTextColor="@color/white" />

    <!-- Image Container -->
    <FrameLayout
        android:id="@+id/imageContainer"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_margin="16dp"
        app:layout_constraintBottom_toTopOf="@+id/controlsContainer"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/toolbar">

        <!-- Background Image -->
        <ImageView
            android:id="@+id/imageView"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:scaleType="fitCenter"
            android:contentDescription="@string/captured_image" />

        <!-- Crop Overlay -->
        <com.example.aicamera.QuadrilateralCropView
            android:id="@+id/cropView"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />

    </FrameLayout>

    <!-- Controls Container -->
    <LinearLayout
        android:id="@+id/controlsContainer"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="16dp"
        android:background="@color/black"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent">

        <!-- Cancel Button -->
        <Button
            android:id="@+id/cancelButton"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:layout_marginEnd="8dp"
            android:text="@string/cancel"
            android:textColor="@color/white"
            android:backgroundTint="@color/purple_700"
            style="@style/Widget.Material3.Button.OutlinedButton" />

        <!-- Reset Button -->
        <Button
            android:id="@+id/resetButton"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:layout_marginStart="4dp"
            android:layout_marginEnd="4dp"
            android:text="@string/reset"
            android:textColor="@color/white"
            android:backgroundTint="@color/purple_500"
            style="@style/Widget.Material3.Button.OutlinedButton" />

        <!-- Crop Button -->
        <Button
            android:id="@+id/cropButton"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:layout_marginStart="8dp"
            android:text="@string/crop_and_continue"
            android:textColor="@color/white"
            android:backgroundTint="@color/purple_500" />

    </LinearLayout>

    <!-- Progress Bar -->
    <ProgressBar
        android:id="@+id/progressBar"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        android:indeterminateTint="@color/white"
        app:layout_constraintBottom_toBottomOf="@+id/imageContainer"
        app:layout_constraintEnd_toEndOf="@+id/imageContainer"
        app:layout_constraintStart_toStartOf="@+id/imageContainer"
        app:layout_constraintTop_toTopOf="@+id/imageContainer" />

    <!-- Status Text -->
    <TextView
        android:id="@+id/statusText"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:text="@string/adjust_crop_area"
        android:textColor="@color/white"
        android:textSize="14sp"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/progressBar" />

</androidx.constraintlayout.widget.ConstraintLayout>
