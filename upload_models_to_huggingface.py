#!/usr/bin/env python3
"""
Upload chess models to Hugging Face Hub
Uploads both V6 segmentation and YOLO piece detection models
"""

import os
import json
import shutil
from huggingface_hub import HfA<PERSON>, create_repo

# Your Hugging Face token
HF_TOKEN = "*************************************"

# Model configurations
MODELS = {
    "v6_segmentation": {
        "repo_name": "your-username/chess-board-segmentation-v6",
        "local_path": r"C:\Users\<USER>\OneDrive\Desktop\fizzi chess\best_model.pth",
        "description": "Breakthrough V6 UNet for chess board segmentation with 0.9391 Dice score",
        "config": {
            "model_type": "unet",
            "task": "image-segmentation",
            "architectures": ["BreakthroughUNetV6"],
            "base_channels": 32,
            "input_size": [256, 256],
            "num_classes": 1,
            "dice_score": 0.9391,
            "license": "apache-2.0",
            "tags": ["chess", "segmentation", "unet", "computer-vision"]
        },
        "readme": """---
license: apache-2.0
tags:
- chess
- segmentation
- unet
- computer-vision
pipeline_tag: image-segmentation
---

# Chess Board Segmentation V6

## Model Description
Breakthrough V6 UNet model for precise chess board segmentation and boundary detection.

## Performance
- **Dice Score**: 0.9391
- **Input Size**: 256x256
- **Architecture**: Custom UNet V6 with 32 base channels

## Usage
```python
import torch
from transformers import AutoModel

model = AutoModel.from_pretrained("your-username/chess-board-segmentation-v6")
# Process 256x256 RGB images
```

## Training
Trained on diverse chess board images with various angles, lighting conditions, and board styles.
"""
    },

    "yolo_pieces": {
        "repo_name": "your-username/chess-piece-detection-yolo11n",
        "local_path": r"C:\Users\<USER>\OneDrive\Desktop\fizzi chess\best.pt",
        "description": "YOLO11n optimized for chess piece detection and classification",
        "config": {
            "model_type": "yolo",
            "task": "object-detection",
            "architectures": ["YOLO11n"],
            "input_size": [416, 416],
            "num_classes": 12,
            "class_names": [
                "white_pawn", "white_knight", "white_bishop", "white_rook", "white_queen", "white_king",
                "black_pawn", "black_knight", "black_bishop", "black_rook", "black_queen", "black_king"
            ],
            "license": "apache-2.0",
            "tags": ["chess", "object-detection", "yolo", "pieces"]
        },
        "readme": """---
license: apache-2.0
tags:
- chess
- object-detection
- yolo
- pieces
pipeline_tag: object-detection
---

# Chess Piece Detection YOLO11n

## Model Description
YOLO11n model optimized for detecting and classifying chess pieces on a board.

## Classes
- **White pieces**: Pawn, Knight, Bishop, Rook, Queen, King
- **Black pieces**: pawn, knight, bishop, rook, queen, king

## Performance
- **Input Size**: 416x416
- **Architecture**: YOLO11n
- **Optimized**: Enhanced GPU training with smart filtering

## Usage
```python
from ultralytics import YOLO

model = YOLO("your-username/chess-piece-detection-yolo11n")
results = model("chess_board.jpg", imgsz=416, conf=0.5)
```

## Training
Trained on diverse chess piece datasets with various board styles, lighting, and angles.
"""
    }
}

def create_model_files(model_info, temp_dir):
    """Create all required files for Hugging Face upload"""

    # Create config.json
    config_path = os.path.join(temp_dir, "config.json")
    with open(config_path, 'w') as f:
        json.dump(model_info["config"], f, indent=2)

    # Create README.md
    readme_path = os.path.join(temp_dir, "README.md")
    with open(readme_path, 'w') as f:
        f.write(model_info["readme"])

    # Copy and rename model file
    if os.path.exists(model_info["local_path"]):
        model_dest = os.path.join(temp_dir, "pytorch_model.bin")
        shutil.copy2(model_info["local_path"], model_dest)
        print(f"✅ Copied model: {model_info['local_path']} -> pytorch_model.bin")
        return True
    else:
        print(f"❌ Model file not found: {model_info['local_path']}")
        return False

def upload_model(model_key, model_info):
    """Upload a single model to Hugging Face"""
    print(f"\n🚀 Uploading {model_key}...")

    # Create temporary directory
    temp_dir = f"temp_{model_key}"
    os.makedirs(temp_dir, exist_ok=True)

    try:
        # Create model files
        if not create_model_files(model_info, temp_dir):
            return False

        # Create repository
        repo_name = model_info["repo_name"]
        print(f"📦 Creating repository: {repo_name}")

        try:
            create_repo(repo_name, token=HF_TOKEN, exist_ok=True)
            print(f"✅ Repository created/exists: {repo_name}")
        except Exception as e:
            print(f"⚠️ Repository creation warning: {e}")

        # Upload files
        api = HfApi()

        # Upload each file
        files_to_upload = ["pytorch_model.bin", "config.json", "README.md"]

        for filename in files_to_upload:
            file_path = os.path.join(temp_dir, filename)
            if os.path.exists(file_path):
                print(f"📤 Uploading {filename}...")
                api.upload_file(
                    path_or_fileobj=file_path,
                    path_in_repo=filename,
                    repo_id=repo_name,
                    token=HF_TOKEN
                )
                print(f"✅ Uploaded {filename}")
            else:
                print(f"⚠️ File not found: {filename}")

        print(f"🎉 Successfully uploaded {model_key}!")
        print(f"🔗 Model URL: https://huggingface.co/{repo_name}")

        return True

    except Exception as e:
        print(f"❌ Upload failed for {model_key}: {e}")
        return False

    finally:
        # Clean up temporary directory
        if os.path.exists(temp_dir):
            shutil.rmtree(temp_dir)

def main():
    """Main upload function"""
    print("🤗 Chess Models Upload to Hugging Face")
    print("=" * 50)
    print(f"🔑 Token: {HF_TOKEN[:10]}...")

    # Update repository names with your actual username
    username = input("Enter your Hugging Face username: ").strip()
    if not username:
        print("❌ Username required")
        return

    # Update repository names
    for model_info in MODELS.values():
        model_info["repo_name"] = model_info["repo_name"].replace("your-username", username)
        model_info["readme"] = model_info["readme"].replace("your-username", username)

    # Upload each model
    success_count = 0
    for model_key, model_info in MODELS.items():
        if upload_model(model_key, model_info):
            success_count += 1

    print(f"\n🎯 Upload Summary:")
    print(f"✅ Successful: {success_count}/{len(MODELS)}")

    if success_count == len(MODELS):
        print("\n🎉 All models uploaded successfully!")
        print("\n📱 Next steps for Android integration:")
        print("1. Update ApiConfig.kt with your model names")
        print("2. Test the models in your Android app")
        print("3. Configure the app to use your custom models")
    else:
        print("\n⚠️ Some uploads failed. Check the error messages above.")

if __name__ == "__main__":
    main()
