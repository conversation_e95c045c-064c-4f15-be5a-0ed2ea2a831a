package com.example.aicamera

import android.Manifest
import android.app.Activity
import android.content.Intent
import android.content.pm.PackageManager
import android.os.Bundle
import android.util.Log
import android.view.View
import android.widget.Toast
import androidx.activity.result.contract.ActivityResultContracts
import androidx.appcompat.app.AppCompatActivity
import androidx.core.content.ContextCompat
import androidx.lifecycle.lifecycleScope
import com.example.aicamera.databinding.ActivityMainBinding
import kotlinx.coroutines.launch
import java.io.File

class MainActivity : AppCompatActivity() {

    companion object {
        private const val TAG = "MainActivity"
        private val REQUIRED_PERMISSIONS = mutableListOf(
            Manifest.permission.CAMERA
        ).toTypedArray()
    }

    private lateinit var binding: ActivityMainBinding
    private lateinit var cameraManager: CameraManager
    private lateinit var networkManager: NetworkManager
    private var capturedImageFile: File? = null
    private var croppedImageFile: File? = null

    // Permission launcher
    private val requestPermissionLauncher = registerForActivityResult(
        ActivityResultContracts.RequestMultiplePermissions()
    ) { permissions ->
        var allGranted = true
        permissions.entries.forEach {
            if (!it.value) allGranted = false
        }

        if (allGranted) {
            startCamera()
        } else {
            showError("Camera permission is required to use this app")
        }
    }

    // Crop activity launcher
    private val cropActivityLauncher = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) { result ->
        if (result.resultCode == Activity.RESULT_OK) {
            val croppedImagePath = result.data?.getStringExtra(CropActivity.EXTRA_CROPPED_IMAGE_PATH)
            if (croppedImagePath != null) {
                croppedImageFile = File(croppedImagePath)
                updateStatus("Image cropped successfully")
                binding.sendToAiButton.isEnabled = true
                showToast("Image ready for AI processing")
            } else {
                showError("Failed to get cropped image")
            }
        } else {
            updateStatus("Cropping cancelled")
            // Re-enable capture button so user can try again
            binding.captureButton.isEnabled = true
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityMainBinding.inflate(layoutInflater)
        setContentView(binding.root)

        // Initialize managers
        cameraManager = CameraManager(this)
        networkManager = NetworkManager()

        // Set up UI listeners
        setupUI()

        // Request permissions and start camera
        if (allPermissionsGranted()) {
            startCamera()
        } else {
            requestPermissionLauncher.launch(REQUIRED_PERMISSIONS)
        }
    }

    private fun setupUI() {
        binding.captureButton.setOnClickListener {
            capturePhoto()
        }

        binding.sendToAiButton.setOnClickListener {
            croppedImageFile?.let { file ->
                sendImageToAI(file)
            }
        }
    }

    private fun startCamera() {
        updateStatus("Starting camera...")

        cameraManager.startCamera(
            previewView = binding.previewView,
            lifecycleOwner = this,
            onCameraReady = {
                updateStatus("Camera ready - tap to capture")
                binding.captureButton.isEnabled = true
            },
            onError = { error ->
                showError("Camera error: $error")
            }
        )
    }

    private fun capturePhoto() {
        binding.captureButton.isEnabled = false
        updateStatus("Capturing photo...")

        cameraManager.capturePhoto(
            onImageCaptured = { file ->
                capturedImageFile = file
                updateStatus("Photo captured - opening crop editor")
                showToast("Photo captured: ${file.name}")

                // Launch crop activity
                launchCropActivity(file)
            },
            onError = { error ->
                showError("Capture failed: $error")
                binding.captureButton.isEnabled = true
            }
        )
    }

    private fun launchCropActivity(imageFile: File) {
        val intent = Intent(this, CropActivity::class.java).apply {
            putExtra(CropActivity.EXTRA_IMAGE_PATH, imageFile.absolutePath)
        }
        cropActivityLauncher.launch(intent)
    }

    private fun sendImageToAI(imageFile: File) {
        binding.sendToAiButton.isEnabled = false
        showProgressBar(true)
        updateStatus("Sending image to AI...")

        lifecycleScope.launch {
            when (val result = networkManager.uploadImage(imageFile)) {
                is NetworkResult.Success -> {
                    val response = result.data
                    if (response.success) {
                        updateStatus("AI processing complete")
                        displayAIResponse(response.result ?: "No result provided")
                        showToast("Image processed successfully")
                    } else {
                        showError("AI processing failed: ${response.error}")
                    }
                }
                is NetworkResult.Error -> {
                    showError("Upload failed: ${result.message}")
                }
                is NetworkResult.Loading -> {
                    updateStatus(result.message)
                }
            }

            showProgressBar(false)
            binding.sendToAiButton.isEnabled = true
        }
    }

    private fun displayAIResponse(response: String) {
        binding.aiResponseText.text = response
    }

    private fun updateStatus(message: String) {
        binding.statusText.text = message
        Log.d(TAG, "Status: $message")
    }

    private fun showProgressBar(show: Boolean) {
        binding.progressBar.visibility = if (show) View.VISIBLE else View.GONE
    }

    private fun showError(message: String) {
        updateStatus("Error: $message")
        showToast(message)
        Log.e(TAG, message)
    }

    private fun showToast(message: String) {
        Toast.makeText(this, message, Toast.LENGTH_SHORT).show()
    }

    private fun allPermissionsGranted() = REQUIRED_PERMISSIONS.all {
        ContextCompat.checkSelfPermission(baseContext, it) == PackageManager.PERMISSION_GRANTED
    }

    override fun onDestroy() {
        super.onDestroy()
        cameraManager.shutdown()
    }
}
