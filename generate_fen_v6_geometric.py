"""
V6 Enhanced FEN Generation with Geometric Grid Division:
Uses V6 segmentation to find exact board boundaries and creates proper 8x8 grid
based on the actual segmented board geometry (not assuming perfect square).
"""

import os
import sys
import cv2
import numpy as np
import torch
from ultralytics import YOLO
import matplotlib.pyplot as plt
import argparse
import time

# Add the project root to the path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import the breakthrough V6 model
from chess_board_detection.models.breakthrough_unet_v6_simple import get_breakthrough_v6_model

# Configuration
CONFIG = {
    "v6_model": "breakthrough_v6_results/best_model.pth",
    "piece_model": "piece_detection/models/yolo11n_416x416_enhanced_gpu/chess_pieces_20250521_091541/weights/best.pt",
    "fen_symbols": {
        "white_pawn": "P", "white_knight": "N", "white_bishop": "B", "white_rook": "R", "white_queen": "Q", "white_king": "K",
        "black_pawn": "p", "black_knight": "n", "black_bishop": "b", "black_rook": "r", "black_queen": "q", "black_king": "k"
    }
}

def load_v6_model(model_path, device):
    """Load the breakthrough V6 segmentation model."""
    print(f"🚀 Loading V6 breakthrough model from: {model_path}")

    model = get_breakthrough_v6_model(base_channels=32)

    if os.path.exists(model_path):
        state_dict = torch.load(model_path, map_location=device, weights_only=True)
        model.load_state_dict(state_dict)
        print("✅ V6 model loaded successfully")
    else:
        print(f"❌ V6 model file not found: {model_path}")
        return None

    model = model.to(device)
    model.eval()

    total_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    print(f"📊 V6 Parameters: {total_params:,}")

    return model

def find_board_corners(mask):
    """
    Find the four corners of the chess board from segmentation mask.
    Returns corners in order: top-left, top-right, bottom-right, bottom-left
    """
    print("🔍 Finding board corners from segmentation...")

    # Find contours
    contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

    if not contours:
        print("❌ No contours found in segmentation mask")
        return None

    # Get the largest contour (chess board)
    largest_contour = max(contours, key=cv2.contourArea)

    # Approximate contour to polygon
    epsilon = 0.02 * cv2.arcLength(largest_contour, True)
    approx = cv2.approxPolyDP(largest_contour, epsilon, True)

    # If we don't get exactly 4 points, use bounding rectangle
    if len(approx) != 4:
        print("⚠️ Contour approximation didn't yield 4 corners, using bounding rectangle")
        x, y, w, h = cv2.boundingRect(largest_contour)
        corners = np.array([
            [x, y],           # top-left
            [x + w, y],       # top-right
            [x + w, y + h],   # bottom-right
            [x, y + h]        # bottom-left
        ], dtype=np.float32)
    else:
        # Sort corners: top-left, top-right, bottom-right, bottom-left
        corners = approx.reshape(4, 2).astype(np.float32)

        # Sort by y-coordinate to get top and bottom pairs
        corners = corners[np.argsort(corners[:, 1])]

        # Sort top two by x-coordinate (left to right)
        top_corners = corners[:2]
        top_corners = top_corners[np.argsort(top_corners[:, 0])]

        # Sort bottom two by x-coordinate (right to left for correct order)
        bottom_corners = corners[2:]
        bottom_corners = bottom_corners[np.argsort(bottom_corners[:, 0])[::-1]]

        # Arrange in order: top-left, top-right, bottom-right, bottom-left
        corners = np.array([
            top_corners[0],    # top-left
            top_corners[1],    # top-right
            bottom_corners[0], # bottom-right
            bottom_corners[1]  # bottom-left
        ], dtype=np.float32)

    print(f"✅ Board corners found: {corners}")
    return corners

def create_perspective_corrected_board(image, corners, output_size=512):
    """
    Create perspective-corrected chess board using the detected corners.
    """
    print("🔄 Creating perspective-corrected board...")

    # Define destination points for a square board
    dst_corners = np.array([
        [0, 0],                           # top-left
        [output_size - 1, 0],             # top-right
        [output_size - 1, output_size - 1], # bottom-right
        [0, output_size - 1]              # bottom-left
    ], dtype=np.float32)

    # Calculate perspective transformation matrix
    transform_matrix = cv2.getPerspectiveTransform(corners, dst_corners)

    # Apply perspective transformation
    corrected_board = cv2.warpPerspective(image, transform_matrix, (output_size, output_size))

    print(f"✅ Perspective correction completed: {output_size}x{output_size}")
    return corrected_board, transform_matrix

def create_chess_grid(board_size=512):
    """
    Create 8x8 chess grid with 7x7 internal lines.
    Returns grid lines and square boundaries.
    """
    print("📐 Creating 8x8 chess grid...")

    # Calculate grid cell size
    cell_size = board_size / 8

    # Create grid lines (7 vertical + 7 horizontal internal lines)
    vertical_lines = []
    horizontal_lines = []

    for i in range(1, 8):  # 7 internal lines
        x = int(i * cell_size)
        y = int(i * cell_size)

        vertical_lines.append([(x, 0), (x, board_size)])
        horizontal_lines.append([(0, y), (board_size, y)])

    # Create square boundaries for each of the 64 squares
    squares = []
    for row in range(8):
        for col in range(8):
            x1 = int(col * cell_size)
            y1 = int(row * cell_size)
            x2 = int((col + 1) * cell_size)
            y2 = int((row + 1) * cell_size)

            squares.append({
                'row': row,
                'col': col,
                'chess_row': 7 - row,  # Chess coordinates (rank 8 at top)
                'chess_col': col,      # Chess coordinates (file a at left)
                'bbox': (x1, y1, x2, y2),
                'center': ((x1 + x2) // 2, (y1 + y2) // 2)
            })

    print(f"✅ Grid created: 64 squares, cell size: {cell_size:.1f}px")
    return vertical_lines, horizontal_lines, squares, cell_size

def detect_chessboard_v6_geometric(model, image_path, device):
    """
    Detect chessboard using V6 and create geometrically correct grid.
    """
    print(f"🔍 V6 Geometric chess board detection: {image_path}")

    # Load image
    original_image = cv2.imread(image_path)
    if original_image is None:
        print(f"❌ Could not load image from {image_path}")
        return None

    image_rgb = cv2.cvtColor(original_image, cv2.COLOR_BGR2RGB)
    original_h, original_w = image_rgb.shape[:2]

    # Resize for V6 model
    target_size = 256
    image_resized = cv2.resize(image_rgb, (target_size, target_size))

    # Normalize and convert to tensor
    image_normalized = image_resized.astype(np.float32) / 255.0
    image_tensor = torch.from_numpy(image_normalized).permute(2, 0, 1).unsqueeze(0).to(device)

    # Run V6 inference
    start_time = time.time()
    with torch.no_grad():
        output = model(image_tensor)
        prediction = torch.sigmoid(output)
    inference_time = (time.time() - start_time) * 1000

    # Convert to numpy and resize back
    mask = prediction.cpu().squeeze().numpy()
    binary_mask = (mask > 0.5).astype(np.uint8)
    mask_resized = cv2.resize(binary_mask, (original_w, original_h))

    print(f"✅ V6 inference: {inference_time:.2f}ms, range: [{mask.min():.4f}, {mask.max():.4f}]")

    # Find board corners
    corners = find_board_corners(mask_resized)
    if corners is None:
        return None

    # Create perspective-corrected board
    board_corrected, transform_matrix = create_perspective_corrected_board(original_image, corners)

    # Create chess grid
    v_lines, h_lines, squares, cell_size = create_chess_grid()

    return {
        'original_image': original_image,
        'mask': mask_resized,
        'corners': corners,
        'board_corrected': board_corrected,
        'transform_matrix': transform_matrix,
        'vertical_lines': v_lines,
        'horizontal_lines': h_lines,
        'squares': squares,
        'cell_size': cell_size,
        'inference_time': inference_time
    }

def enhance_board_for_detection(board_image):
    """
    Apply conservative preprocessing to enhance piece detection without amplifying board markings.
    Focuses on piece contrast rather than overall sharpening.
    """
    print("🔧 Applying conservative board enhancement...")

    # Convert to RGB if needed
    if len(board_image.shape) == 3:
        board_rgb = cv2.cvtColor(board_image, cv2.COLOR_BGR2RGB)
    else:
        board_rgb = board_image

    # Apply very mild histogram equalization to avoid amplifying board markings
    lab = cv2.cvtColor(board_rgb, cv2.COLOR_RGB2LAB)
    lab[:,:,0] = cv2.createCLAHE(clipLimit=1.5, tileGridSize=(4,4)).apply(lab[:,:,0])
    enhanced = cv2.cvtColor(lab, cv2.COLOR_LAB2RGB)

    # Apply very subtle sharpening only (reduce intensity to avoid board markings)
    kernel = np.array([[0,-0.5,0], [-0.5,3,-0.5], [0,-0.5,0]])
    sharpened = cv2.filter2D(enhanced, -1, kernel)

    # Blend with more weight on original to reduce false positives
    enhanced_final = cv2.addWeighted(enhanced, 0.85, sharpened, 0.15, 0)

    # Ensure proper data type
    enhanced_final = np.clip(enhanced_final, 0, 255).astype(np.uint8)

    print("✅ Conservative enhancement completed")
    return enhanced_final

def detect_pieces(model_path, board_image):
    """
    Detect chess pieces using YOLO model with conservative preprocessing and smart overlap filtering.
    Uses 50% overlap threshold with higher confidence priority.
    """
    print("🎯 Detecting chess pieces with smart filtering...")

    # Load YOLO model
    model = YOLO(model_path)

    # Try both original and enhanced board for comparison
    enhanced_board = enhance_board_for_detection(board_image)

    print("🔍 Running YOLO inference on original board...")
    # Primary detection on original board with higher confidence to avoid board markings
    results_original = model(board_image, imgsz=416, conf=0.5, iou=0.7)[0]

    print("🔍 Running YOLO inference on enhanced board...")
    # Secondary detection on enhanced board with moderate confidence for missed pieces
    results_enhanced = model(enhanced_board, imgsz=416, conf=0.4, iou=0.7)[0]

    # Collect all detections
    all_detections = []
    class_names = results_original.names

    # Add original board detections (higher priority)
    if len(results_original.boxes) > 0:
        boxes = results_original.boxes.xyxy.cpu().numpy()
        scores = results_original.boxes.conf.cpu().numpy()
        class_ids = results_original.boxes.cls.cpu().numpy()

        for box, score, class_id in zip(boxes, scores, class_ids):
            all_detections.append({
                'bbox': box,
                'confidence': score,
                'class_id': int(class_id),
                'class_name': class_names[int(class_id)],
                'source': 'original'
            })

    # Add enhanced board detections (lower priority)
    if len(results_enhanced.boxes) > 0:
        boxes = results_enhanced.boxes.xyxy.cpu().numpy()
        scores = results_enhanced.boxes.conf.cpu().numpy()
        class_ids = results_enhanced.boxes.cls.cpu().numpy()

        for box, score, class_id in zip(boxes, scores, class_ids):
            all_detections.append({
                'bbox': box,
                'confidence': score,
                'class_id': int(class_id),
                'class_name': class_names[int(class_id)],
                'source': 'enhanced'
            })

    # Apply board marking filtering first
    print("🧹 Filtering out board markings...")
    marking_filtered = filter_board_markings(all_detections, board_image)

    # Apply smart overlap filtering with 50% threshold
    print("🧠 Applying smart overlap filtering (50% threshold)...")
    filtered_pieces = apply_smart_overlap_filtering(marking_filtered)

    # Convert to final format
    pieces = []
    for detection in filtered_pieces:
        x1, y1, x2, y2 = detection['bbox']
        center_x = (x1 + x2) / 2
        center_y = (y1 + y2) / 2

        pieces.append({
            'bbox': detection['bbox'],
            'center': (center_x, center_y),
            'confidence': detection['confidence'],
            'class_id': detection['class_id'],
            'class_name': detection['class_name'],
            'source': detection['source']
        })

    # Sort by confidence (highest first)
    pieces = sorted(pieces, key=lambda p: p['confidence'], reverse=True)

    print(f"✅ Detected {len(pieces)} chess pieces (smart filtering)")

    # Print detection summary with sources
    piece_counts = {}
    source_counts = {'original': 0, 'enhanced': 0}
    for piece in pieces:
        piece_name = piece['class_name']
        piece_counts[piece_name] = piece_counts.get(piece_name, 0) + 1
        source_counts[piece['source']] += 1

    print("📊 Detection summary:")
    for piece_name, count in sorted(piece_counts.items()):
        print(f"   {piece_name}: {count}")
    print(f"📊 Source breakdown: Original: {source_counts['original']}, Enhanced: {source_counts['enhanced']}")

    return pieces

def filter_board_markings(detections, board_image):
    """
    Filter out board markings and low-opacity starting position indicators.
    Uses visual analysis to distinguish real pieces from board markings.
    """
    print(f"🔍 Analyzing {len(detections)} detections for board markings...")

    filtered = []

    for detection in detections:
        bbox = detection['bbox']
        confidence = detection['confidence']

        # Extract the region of interest
        x1, y1, x2, y2 = bbox.astype(int)
        roi = board_image[y1:y2, x1:x2]

        # Skip if ROI is too small
        if roi.shape[0] < 10 or roi.shape[1] < 10:
            print(f"   Rejecting {detection['class_name']} (conf: {confidence:.3f}) - ROI too small")
            continue

        # Convert to grayscale for analysis
        if len(roi.shape) == 3:
            roi_gray = cv2.cvtColor(roi, cv2.COLOR_BGR2GRAY)
        else:
            roi_gray = roi

        # Calculate visual characteristics
        mean_intensity = np.mean(roi_gray)
        std_intensity = np.std(roi_gray)
        contrast_ratio = std_intensity / (mean_intensity + 1e-6)

        # Calculate edge strength
        edges = cv2.Canny(roi_gray, 50, 150)
        edge_density = np.sum(edges > 0) / (edges.shape[0] * edges.shape[1])

        # Filtering criteria for board markings
        is_board_marking = False
        rejection_reason = ""

        # 1. Very low confidence (likely faint markings)
        if confidence < 0.45:
            is_board_marking = True
            rejection_reason = "very low confidence"

        # 2. Low contrast (faint/transparent markings)
        elif contrast_ratio < 0.15:
            is_board_marking = True
            rejection_reason = "low contrast (faint marking)"

        # 3. Very low edge density (not a solid piece)
        elif edge_density < 0.02:
            is_board_marking = True
            rejection_reason = "low edge density (transparent)"

        # 4. Combination of moderate confidence + low visual features
        elif confidence < 0.6 and contrast_ratio < 0.25 and edge_density < 0.05:
            is_board_marking = True
            rejection_reason = "weak visual features"

        if is_board_marking:
            print(f"   Rejecting {detection['class_name']} (conf: {confidence:.3f}) - {rejection_reason}")
            print(f"     Visual: contrast={contrast_ratio:.3f}, edges={edge_density:.3f}, intensity={mean_intensity:.1f}")
        else:
            filtered.append(detection)
            print(f"   Keeping {detection['class_name']} (conf: {confidence:.3f}) - solid piece")

    print(f"✅ Board marking filter: {len(detections)} → {len(filtered)} detections")
    return filtered

def apply_smart_overlap_filtering(detections, overlap_threshold=0.5):
    """
    Apply smart overlap filtering with 50% threshold.
    When overlap > 50%, keep the detection with higher confidence.
    """
    print(f"🔍 Filtering {len(detections)} detections with {overlap_threshold*100}% overlap threshold...")

    # Sort by confidence (highest first)
    detections = sorted(detections, key=lambda d: d['confidence'], reverse=True)

    filtered = []

    for current in detections:
        should_keep = True

        # Check against all already accepted detections
        for accepted in filtered:
            overlap = calculate_bbox_overlap(current['bbox'], accepted['bbox'])

            if overlap > overlap_threshold:
                # Overlap is too high, don't keep this detection
                # (since we sorted by confidence, accepted detection has higher confidence)
                should_keep = False
                print(f"   Rejecting {current['class_name']} (conf: {current['confidence']:.3f}) - {overlap*100:.1f}% overlap with {accepted['class_name']} (conf: {accepted['confidence']:.3f})")
                break

        if should_keep:
            filtered.append(current)

    print(f"✅ Kept {len(filtered)} detections after overlap filtering")
    return filtered

def calculate_bbox_overlap(box1, box2):
    """
    Calculate overlap percentage between two bounding boxes.
    Returns the overlap as a percentage of the smaller box area.
    """
    x1_1, y1_1, x2_1, y2_1 = box1
    x1_2, y1_2, x2_2, y2_2 = box2

    # Calculate intersection
    x1_i = max(x1_1, x1_2)
    y1_i = max(y1_1, y1_2)
    x2_i = min(x2_1, x2_2)
    y2_i = min(y2_1, y2_2)

    if x2_i <= x1_i or y2_i <= y1_i:
        return 0.0

    intersection = (x2_i - x1_i) * (y2_i - y1_i)

    # Calculate areas
    area1 = (x2_1 - x1_1) * (y2_1 - y1_1)
    area2 = (x2_2 - x1_2) * (y2_2 - y1_2)

    # Return overlap as percentage of smaller box
    smaller_area = min(area1, area2)
    return intersection / smaller_area if smaller_area > 0 else 0.0

def calculate_iou(box1, box2):
    """Calculate Intersection over Union (IoU) of two bounding boxes."""
    x1_1, y1_1, x2_1, y2_1 = box1
    x1_2, y1_2, x2_2, y2_2 = box2

    # Calculate intersection
    x1_i = max(x1_1, x1_2)
    y1_i = max(y1_1, y1_2)
    x2_i = min(x2_1, x2_2)
    y2_i = min(y2_1, y2_2)

    if x2_i <= x1_i or y2_i <= y1_i:
        return 0.0

    intersection = (x2_i - x1_i) * (y2_i - y1_i)

    # Calculate union
    area1 = (x2_1 - x1_1) * (y2_1 - y1_1)
    area2 = (x2_2 - x1_2) * (y2_2 - y1_2)
    union = area1 + area2 - intersection

    return intersection / union if union > 0 else 0.0

def map_pieces_to_geometric_grid(pieces, squares):
    """Map pieces to geometric grid squares based on overlap."""
    print("🗺️ Mapping pieces to geometric grid...")

    # Initialize grid
    grid = [[None for _ in range(8)] for _ in range(8)]

    # Sort pieces by confidence
    sorted_pieces = sorted(pieces, key=lambda p: p['confidence'], reverse=True)

    for piece in sorted_pieces:
        piece_center = piece['center']
        best_square = None

        # Find which square contains the piece center
        for square in squares:
            x1, y1, x2, y2 = square['bbox']
            if x1 <= piece_center[0] <= x2 and y1 <= piece_center[1] <= y2:
                best_square = square
                break

        if best_square:
            chess_row = best_square['chess_row']
            chess_col = best_square['chess_col']

            # Place piece if square is empty
            if grid[chess_row][chess_col] is None:
                piece_with_pos = piece.copy()
                piece_with_pos['square'] = best_square
                grid[chess_row][chess_col] = piece_with_pos

    return grid

def generate_fen(grid):
    """
    Generate FEN notation from piece grid.
    FEN starts from rank 8 (top of board) and goes down to rank 1.
    Bottom-left is a1, bottom-right is h1.
    """
    print("📝 Generating FEN notation...")

    fen_rows = []

    # FEN notation starts from rank 8 (index 0 in our grid) and goes to rank 1 (index 7)
    # Since grid[chess_row][chess_col] where chess_row = 7 - row,
    # we need to iterate through chess_row from 7 down to 0
    for chess_rank in range(7, -1, -1):  # 7, 6, 5, 4, 3, 2, 1, 0 (rank 8 to rank 1)
        empty_count = 0
        row_fen = ""

        # Iterate through files a-h (left to right)
        for chess_file in range(8):  # 0 to 7 (files a to h)
            square = grid[chess_rank][chess_file]

            if square is None:
                empty_count += 1
            else:
                if empty_count > 0:
                    row_fen += str(empty_count)
                    empty_count = 0

                class_name = square['class_name']
                if class_name in CONFIG["fen_symbols"]:
                    row_fen += CONFIG["fen_symbols"][class_name]
                else:
                    print(f"⚠️ Unknown piece: {class_name}")
                    row_fen += "?"

        if empty_count > 0:
            row_fen += str(empty_count)

        fen_rows.append(row_fen)

    fen = "/".join(fen_rows)
    print(f"✅ FEN: {fen}")
    return fen

def visualize_geometric_results(results, pieces, grid, fen, output_path, enhanced_board=None):
    """Create comprehensive visualization with geometric grid."""
    print("🎨 Creating geometric visualization...")

    fig, axes = plt.subplots(3, 3, figsize=(18, 18))
    fig.suptitle('V6 Enhanced Geometric FEN Generation', fontsize=16, fontweight='bold')

    # Original with corners
    img_with_corners = results['original_image'].copy()
    corners = results['corners'].astype(int)
    cv2.polylines(img_with_corners, [corners], True, (0, 255, 0), 3)
    for i, corner in enumerate(corners):
        cv2.circle(img_with_corners, tuple(corner), 8, (255, 0, 0), -1)
        cv2.putText(img_with_corners, str(i), tuple(corner + 10),
                   cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)

    axes[0, 0].imshow(cv2.cvtColor(img_with_corners, cv2.COLOR_BGR2RGB))
    axes[0, 0].set_title('Original with Detected Corners')
    axes[0, 0].axis('off')

    # V6 segmentation
    axes[0, 1].imshow(results['mask'], cmap='hot')
    axes[0, 1].set_title(f'V6 Segmentation\n({results["inference_time"]:.1f}ms)')
    axes[0, 1].axis('off')

    # Perspective corrected board
    axes[0, 2].imshow(cv2.cvtColor(results['board_corrected'], cv2.COLOR_BGR2RGB))
    axes[0, 2].set_title('Perspective Corrected Board')
    axes[0, 2].axis('off')

    # Enhanced board for detection
    if enhanced_board is not None:
        axes[1, 0].imshow(enhanced_board)
        axes[1, 0].set_title('Enhanced Board for Detection')
        axes[1, 0].axis('off')
    else:
        axes[1, 0].imshow(cv2.cvtColor(results['board_corrected'], cv2.COLOR_BGR2RGB))
        axes[1, 0].set_title('Board for Detection')
        axes[1, 0].axis('off')

    # Board with geometric grid
    board_with_grid = results['board_corrected'].copy()

    # Draw grid lines
    for line in results['vertical_lines']:
        cv2.line(board_with_grid, line[0], line[1], (255, 255, 255), 2)
    for line in results['horizontal_lines']:
        cv2.line(board_with_grid, line[0], line[1], (255, 255, 255), 2)

    # Draw border
    cv2.rectangle(board_with_grid, (0, 0), (511, 511), (255, 255, 255), 2)

    axes[1, 1].imshow(cv2.cvtColor(board_with_grid, cv2.COLOR_BGR2RGB))
    axes[1, 1].set_title('Geometric 8x8 Grid')
    axes[1, 1].axis('off')

    # Pieces on grid
    board_with_pieces = results['board_corrected'].copy()

    # Draw grid
    for line in results['vertical_lines']:
        cv2.line(board_with_pieces, line[0], line[1], (200, 200, 200), 1)
    for line in results['horizontal_lines']:
        cv2.line(board_with_pieces, line[0], line[1], (200, 200, 200), 1)

    # Draw pieces with confidence and source colors
    for piece in pieces:
        x1, y1, x2, y2 = piece['bbox'].astype(int)

        # Color based on source and confidence
        conf = piece['confidence']
        source = piece.get('source', 'unknown')

        if source == 'original':
            if conf > 0.7:
                color = (0, 255, 0)  # Bright green for high confidence original
            elif conf > 0.4:
                color = (0, 200, 0)  # Medium green for medium confidence original
            else:
                color = (0, 150, 0)  # Dark green for low confidence original
        else:  # enhanced
            if conf > 0.7:
                color = (255, 165, 0)  # Orange for high confidence enhanced
            elif conf > 0.4:
                color = (255, 140, 0)  # Medium orange for medium confidence enhanced
            else:
                color = (255, 100, 0)  # Dark orange for low confidence enhanced

        cv2.rectangle(board_with_pieces, (x1, y1), (x2, y2), color, 2)
        cv2.putText(board_with_pieces, f"{piece['class_name'][:5]} {conf:.2f}",
                   (x1, y1-5), cv2.FONT_HERSHEY_SIMPLEX, 0.3, color, 1)

        # Add source indicator
        source_indicator = "O" if source == 'original' else "E"
        cv2.putText(board_with_pieces, source_indicator,
                   (x2-15, y1+15), cv2.FONT_HERSHEY_SIMPLEX, 0.4, color, 1)

    axes[1, 2].imshow(cv2.cvtColor(board_with_pieces, cv2.COLOR_BGR2RGB))
    axes[1, 2].set_title(f'Enhanced Detection ({len(pieces)} pieces)')
    axes[1, 2].axis('off')

    # Chess grid with pieces mapped
    board_with_chess_grid = results['board_corrected'].copy()
    cell_size = 512 // 8

    # Draw chess grid
    for i in range(9):
        cv2.line(board_with_chess_grid, (i * cell_size, 0), (i * cell_size, 512), (255, 255, 255), 1)
        cv2.line(board_with_chess_grid, (0, i * cell_size), (512, i * cell_size), (255, 255, 255), 1)

    # Draw pieces on chess grid
    for chess_rank in range(8):  # 0 to 7 (chess ranks 1 to 8)
        for chess_file in range(8):  # 0 to 7 (chess files a to h)
            piece = grid[chess_rank][chess_file]
            if piece is not None:
                # Calculate visual position (chess_rank 0 = rank 1 = bottom of board)
                center_x = chess_file * cell_size + cell_size // 2
                center_y = (7 - chess_rank) * cell_size + cell_size // 2

                symbol = CONFIG["fen_symbols"].get(piece['class_name'], '?')
                cv2.putText(board_with_chess_grid, symbol,
                           (center_x - 10, center_y + 10),
                           cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)

                # Draw chess coordinate (file a-h, rank 1-8)
                coord = f"{chr(97 + chess_file)}{chess_rank + 1}"
                cv2.putText(board_with_chess_grid, coord,
                           (center_x - 15, center_y - 20),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.4, (255, 255, 255), 1)

    axes[2, 0].imshow(cv2.cvtColor(board_with_chess_grid, cv2.COLOR_BGR2RGB))
    axes[2, 0].set_title('Chess Grid Mapping')
    axes[2, 0].axis('off')

    # Detection statistics
    piece_counts = {}
    conf_stats = []
    for piece in pieces:
        piece_name = piece['class_name']
        piece_counts[piece_name] = piece_counts.get(piece_name, 0) + 1
        conf_stats.append(piece['confidence'])

    stats_text = f"Detection Statistics:\n\n"
    stats_text += f"Total Pieces: {len(pieces)}\n"
    stats_text += f"Avg Confidence: {np.mean(conf_stats):.3f}\n"
    stats_text += f"Min Confidence: {np.min(conf_stats):.3f}\n"
    stats_text += f"Max Confidence: {np.max(conf_stats):.3f}\n\n"
    stats_text += "Piece Counts:\n"
    for piece_name, count in sorted(piece_counts.items()):
        stats_text += f"• {piece_name}: {count}\n"

    axes[2, 1].text(0.1, 0.5, stats_text, fontsize=9, fontfamily='monospace',
                   bbox=dict(boxstyle="round,pad=0.5", facecolor="lightblue"))
    axes[2, 1].set_title('Detection Statistics')
    axes[2, 1].axis('off')

    # FEN result
    fen_text = f"FEN Position:\n{fen}\n\n"
    fen_text += "Enhancements:\n"
    fen_text += "• V6 Segmentation (0.9391 Dice)\n"
    fen_text += "• Geometric Grid Correction\n"
    fen_text += "• Enhanced Preprocessing\n"
    fen_text += "• Board Marking Filtering\n"
    fen_text += "• Multi-threshold Detection\n"
    fen_text += "• IoU-based Filtering\n\n"
    fen_text += f"Model: YOLO11n (416x416)\n"
    fen_text += f"Pieces Detected: {len(pieces)}"

    axes[2, 2].text(0.1, 0.5, fen_text, fontsize=9, fontfamily='monospace',
                   bbox=dict(boxstyle="round,pad=0.5", facecolor="lightgreen"))
    axes[2, 2].set_title('FEN Result')
    axes[2, 2].axis('off')

    plt.tight_layout()
    plt.savefig(output_path, dpi=300, bbox_inches='tight')
    plt.close()

    print(f"💾 Enhanced visualization saved: {output_path}")

def main():
    """Main function for V6 geometric FEN generation."""
    parser = argparse.ArgumentParser(description='V6 Geometric FEN Generation')
    parser.add_argument('image_path', type=str, help='Path to chess board image')
    parser.add_argument('--output', type=str, default='v6_geometric_fen.png', help='Output path')
    parser.add_argument('--v6_model', type=str, default=CONFIG["v6_model"], help='V6 model path')
    parser.add_argument('--piece_model', type=str, default=CONFIG["piece_model"], help='YOLO model path')
    args = parser.parse_args()

    print("🚀 V6 GEOMETRIC FEN GENERATION")
    print("=" * 50)
    print(f"📸 Image: {args.image_path}")

    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"🖥️ Device: {device}")

    try:
        # Load V6 model
        v6_model = load_v6_model(args.v6_model, device)
        if v6_model is None:
            return

        # Geometric board detection
        print("\n🔍 Stage 1: V6 Geometric Board Detection")
        results = detect_chessboard_v6_geometric(v6_model, args.image_path, device)
        if results is None:
            print("❌ Board detection failed")
            return

        # Piece detection with enhancement
        print("\n🎯 Stage 2: Enhanced Piece Detection")
        enhanced_board = enhance_board_for_detection(results['board_corrected'])
        pieces = detect_pieces(args.piece_model, results['board_corrected'])

        # Geometric mapping
        print("\n🗺️ Stage 3: Geometric Mapping")
        grid = map_pieces_to_geometric_grid(pieces, results['squares'])
        fen = generate_fen(grid)

        # Visualization
        print("\n🎨 Stage 4: Enhanced Visualization")
        visualize_geometric_results(results, pieces, grid, fen, args.output, enhanced_board)

        # Save FEN
        fen_file = os.path.splitext(args.output)[0] + ".fen"
        with open(fen_file, 'w') as f:
            f.write(fen)

        print(f"\n✅ SUCCESS!")
        print(f"📝 FEN: {fen}")
        print(f"💾 Files: {args.output}, {fen_file}")

    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
