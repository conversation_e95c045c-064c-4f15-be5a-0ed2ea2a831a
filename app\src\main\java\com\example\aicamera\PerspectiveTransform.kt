package com.example.aicamera

import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.graphics.PointF
import android.util.Log
import org.opencv.android.OpenCVLoaderCallback
import org.opencv.android.OpenCVLoader
import org.opencv.android.Utils
import org.opencv.core.*
import org.opencv.imgproc.Imgproc
import java.io.File
import kotlin.math.max
import kotlin.math.sqrt

/**
 * Utility class for perspective transformation and quadrilateral cropping
 * Uses OpenCV for high-quality perspective correction
 */
class PerspectiveTransform {
    
    companion object {
        private const val TAG = "PerspectiveTransform"
        private const val OUTPUT_SIZE = 800 // Output image size (square)
        
        private var isOpenCVInitialized = false
        
        /**
         * Initialize OpenCV library
         */
        fun initializeOpenCV(callback: (Boolean) -> Unit) {
            if (isOpenCVInitialized) {
                callback(true)
                return
            }
            
            val loaderCallback = object : OpenCVLoaderCallback() {
                override fun onManagerConnected(status: Int) {
                    when (status) {
                        OpenCVLoaderCallback.SUCCESS -> {
                            Log.d(TAG, "OpenCV loaded successfully")
                            isOpenCVInitialized = true
                            callback(true)
                        }
                        else -> {
                            Log.e(TAG, "OpenCV initialization failed")
                            callback(false)
                        }
                    }
                }
                
                override fun onPackageInstall(operation: Int, callback: OpenCVLoaderCallback.InstallCallbackInterface?) {
                    super.onPackageInstall(operation, callback)
                }
            }
            
            if (!OpenCVLoader.initDebug()) {
                Log.d(TAG, "Internal OpenCV library not found. Using OpenCV Manager for initialization")
                OpenCVLoader.initAsync(OpenCVLoader.OPENCV_VERSION, null, loaderCallback)
            } else {
                Log.d(TAG, "OpenCV library found inside package. Using it!")
                loaderCallback.onManagerConnected(OpenCVLoaderCallback.SUCCESS)
            }
        }
    }
    
    /**
     * Apply perspective transformation to crop and correct a quadrilateral region
     * @param inputFile The input image file
     * @param outputFile The output file to save the transformed image
     * @param corners The four corner points of the quadrilateral (in order: top-left, top-right, bottom-right, bottom-left)
     * @return True if transformation was successful, false otherwise
     */
    fun transformQuadrilateral(
        inputFile: File,
        outputFile: File,
        corners: List<PointF>
    ): Boolean {
        if (!isOpenCVInitialized) {
            Log.e(TAG, "OpenCV not initialized")
            return false
        }
        
        if (corners.size != 4) {
            Log.e(TAG, "Exactly 4 corner points required")
            return false
        }
        
        return try {
            // Load the input image
            val bitmap = BitmapFactory.decodeFile(inputFile.absolutePath)
            if (bitmap == null) {
                Log.e(TAG, "Failed to load input image")
                return false
            }
            
            // Convert bitmap to OpenCV Mat
            val inputMat = Mat()
            Utils.bitmapToMat(bitmap, inputMat)
            
            // Convert to RGB (OpenCV uses BGR by default)
            Imgproc.cvtColor(inputMat, inputMat, Imgproc.COLOR_RGBA2RGB)
            
            // Create source points from corners
            val srcPoints = MatOfPoint2f()
            val srcArray = arrayOf(
                Point(corners[0].x.toDouble(), corners[0].y.toDouble()), // top-left
                Point(corners[1].x.toDouble(), corners[1].y.toDouble()), // top-right
                Point(corners[2].x.toDouble(), corners[2].y.toDouble()), // bottom-right
                Point(corners[3].x.toDouble(), corners[3].y.toDouble())  // bottom-left
            )
            srcPoints.fromArray(*srcArray)
            
            // Calculate optimal output size based on the quadrilateral
            val outputSize = calculateOptimalSize(corners)
            
            // Create destination points (perfect rectangle)
            val dstPoints = MatOfPoint2f()
            val dstArray = arrayOf(
                Point(0.0, 0.0),                           // top-left
                Point(outputSize.toDouble(), 0.0),         // top-right
                Point(outputSize.toDouble(), outputSize.toDouble()), // bottom-right
                Point(0.0, outputSize.toDouble())          // bottom-left
            )
            dstPoints.fromArray(*dstArray)
            
            // Calculate perspective transformation matrix
            val transformMatrix = Imgproc.getPerspectiveTransform(srcPoints, dstPoints)
            
            // Apply perspective transformation
            val outputMat = Mat()
            Imgproc.warpPerspective(
                inputMat, 
                outputMat, 
                transformMatrix, 
                Size(outputSize.toDouble(), outputSize.toDouble())
            )
            
            // Convert back to bitmap
            val outputBitmap = Bitmap.createBitmap(
                outputSize, 
                outputSize, 
                Bitmap.Config.ARGB_8888
            )
            Utils.matToBitmap(outputMat, outputBitmap)
            
            // Save the output bitmap
            outputFile.outputStream().use { out ->
                outputBitmap.compress(Bitmap.CompressFormat.JPEG, 90, out)
            }
            
            // Clean up
            inputMat.release()
            outputMat.release()
            transformMatrix.release()
            srcPoints.release()
            dstPoints.release()
            bitmap.recycle()
            outputBitmap.recycle()
            
            Log.d(TAG, "Perspective transformation completed successfully")
            true
            
        } catch (e: Exception) {
            Log.e(TAG, "Error during perspective transformation", e)
            false
        }
    }
    
    /**
     * Calculate optimal output size based on the quadrilateral dimensions
     */
    private fun calculateOptimalSize(corners: List<PointF>): Int {
        // Calculate distances between corners
        val topWidth = distance(corners[0], corners[1])
        val bottomWidth = distance(corners[3], corners[2])
        val leftHeight = distance(corners[0], corners[3])
        val rightHeight = distance(corners[1], corners[2])
        
        // Use the maximum dimension to preserve detail
        val maxDimension = max(max(topWidth, bottomWidth), max(leftHeight, rightHeight))
        
        // Clamp to reasonable bounds
        return when {
            maxDimension > 1200 -> 1200
            maxDimension < 400 -> 400
            else -> maxDimension.toInt()
        }
    }
    
    /**
     * Calculate distance between two points
     */
    private fun distance(p1: PointF, p2: PointF): Float {
        val dx = p1.x - p2.x
        val dy = p1.y - p2.y
        return sqrt(dx * dx + dy * dy)
    }
    
    /**
     * Order points in clockwise order starting from top-left
     * This ensures consistent ordering for perspective transformation
     */
    fun orderPoints(points: List<PointF>): List<PointF> {
        if (points.size != 4) return points
        
        // Calculate centroid
        val centerX = points.sumOf { it.x.toDouble() } / 4
        val centerY = points.sumOf { it.y.toDouble() } / 4
        
        // Sort points by angle from center
        val sortedPoints = points.sortedBy { point ->
            kotlin.math.atan2(point.y - centerY, point.x - centerX)
        }
        
        // Find top-left point (minimum x + y)
        val topLeftIndex = sortedPoints.indices.minByOrNull { i ->
            sortedPoints[i].x + sortedPoints[i].y
        } ?: 0
        
        // Reorder starting from top-left, going clockwise
        val orderedPoints = mutableListOf<PointF>()
        for (i in 0 until 4) {
            orderedPoints.add(sortedPoints[(topLeftIndex + i) % 4])
        }
        
        return orderedPoints
    }
}
