package com.example.aicamera

import android.content.Context
import android.content.SharedPreferences
import android.os.Bundle
import android.view.View
import android.widget.AdapterView
import android.widget.ArrayAdapter
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import com.example.aicamera.databinding.ActivitySettingsBinding

class SettingsActivity : AppCompatActivity() {

    companion object {
        private const val PREFS_NAME = "ai_camera_settings"
        private const val KEY_API_PROVIDER = "api_provider"
        private const val KEY_HF_TOKEN = "hf_token"
        private const val KEY_HF_MODEL = "hf_model"
    }

    private lateinit var binding: ActivitySettingsBinding
    private lateinit var prefs: SharedPreferences

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivitySettingsBinding.inflate(layoutInflater)
        setContentView(binding.root)

        // Set up toolbar
        setSupportActionBar(binding.toolbar)
        supportActionBar?.setDisplayHomeAsUpEnabled(true)
        supportActionBar?.title = "Settings"

        // Initialize preferences
        prefs = getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)

        // Set up UI
        setupUI()
        loadSettings()
    }

    private fun setupUI() {
        // API Provider Spinner
        val providers = arrayOf("Hugging Face Cloud", "Local Server")
        val providerAdapter = ArrayAdapter(this, android.R.layout.simple_spinner_item, providers)
        providerAdapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item)
        binding.apiProviderSpinner.adapter = providerAdapter

        binding.apiProviderSpinner.onItemSelectedListener = object : AdapterView.OnItemSelectedListener {
            override fun onItemSelected(parent: AdapterView<*>?, view: View?, position: Int, id: Long) {
                updateUIForProvider(position)
            }
            override fun onNothingSelected(parent: AdapterView<*>?) {}
        }

        // Hugging Face Model Spinner
        val models = arrayOf(
            "Image Captioning (BLIP)" to ApiConfig.HuggingFace.IMAGE_CAPTIONING_MODEL,
            "Document OCR (TrOCR)" to ApiConfig.HuggingFace.DOCUMENT_OCR_MODEL,
            "Vision Transformer" to ApiConfig.HuggingFace.GENERAL_VIT_MODEL,
            "GIT Model" to ApiConfig.HuggingFace.GIT_MODEL,
            "Chess Board Segmentation V6" to ApiConfig.HuggingFace.CHESS_BOARD_SEGMENTATION,
            "Chess Piece Detection YOLO" to ApiConfig.HuggingFace.CHESS_PIECE_DETECTION
        )

        val modelNames = models.map { it.first }.toTypedArray()
        val modelAdapter = ArrayAdapter(this, android.R.layout.simple_spinner_item, modelNames)
        modelAdapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item)
        binding.modelSpinner.adapter = modelAdapter

        // Save button
        binding.saveButton.setOnClickListener {
            saveSettings()
        }

        // Test connection button
        binding.testConnectionButton.setOnClickListener {
            testConnection()
        }
    }

    private fun updateUIForProvider(position: Int) {
        val isHuggingFace = position == 0

        // Show/hide Hugging Face specific settings
        binding.huggingFaceSection.visibility = if (isHuggingFace) View.VISIBLE else View.GONE
        binding.localServerSection.visibility = if (isHuggingFace) View.GONE else View.VISIBLE
    }

    private fun loadSettings() {
        // Load API provider
        val providerOrdinal = prefs.getInt(KEY_API_PROVIDER, ApiConfig.ApiProvider.HUGGING_FACE.ordinal)
        val provider = ApiConfig.ApiProvider.values().getOrNull(providerOrdinal) ?: ApiConfig.ApiProvider.HUGGING_FACE

        binding.apiProviderSpinner.setSelection(if (provider == ApiConfig.ApiProvider.HUGGING_FACE) 0 else 1)

        // Load Hugging Face settings
        val hfToken = prefs.getString(KEY_HF_TOKEN, "") ?: ""
        binding.tokenEditText.setText(hfToken)

        val hfModel = prefs.getString(KEY_HF_MODEL, ApiConfig.HuggingFace.IMAGE_CAPTIONING_MODEL) ?: ApiConfig.HuggingFace.IMAGE_CAPTIONING_MODEL
        val modelIndex = when (hfModel) {
            ApiConfig.HuggingFace.IMAGE_CAPTIONING_MODEL -> 0
            ApiConfig.HuggingFace.DOCUMENT_OCR_MODEL -> 1
            ApiConfig.HuggingFace.GENERAL_VIT_MODEL -> 2
            ApiConfig.HuggingFace.GIT_MODEL -> 3
            ApiConfig.HuggingFace.CHESS_BOARD_SEGMENTATION -> 4
            ApiConfig.HuggingFace.CHESS_PIECE_DETECTION -> 5
            else -> 0
        }
        binding.modelSpinner.setSelection(modelIndex)

        // Update UI
        updateUIForProvider(binding.apiProviderSpinner.selectedItemPosition)
    }

    private fun saveSettings() {
        val editor = prefs.edit()

        // Save API provider
        val isHuggingFace = binding.apiProviderSpinner.selectedItemPosition == 0
        val provider = if (isHuggingFace) ApiConfig.ApiProvider.HUGGING_FACE else ApiConfig.ApiProvider.LOCAL_SERVER
        editor.putInt(KEY_API_PROVIDER, provider.ordinal)

        // Save Hugging Face settings
        if (isHuggingFace) {
            val token = binding.tokenEditText.text.toString().trim()
            if (token.isEmpty()) {
                showToast("Please enter your Hugging Face API token")
                return
            }

            if (!token.startsWith("hf_")) {
                showToast("Invalid token format. Hugging Face tokens start with 'hf_'")
                return
            }

            editor.putString(KEY_HF_TOKEN, token)

            val selectedModel = when (binding.modelSpinner.selectedItemPosition) {
                0 -> ApiConfig.HuggingFace.IMAGE_CAPTIONING_MODEL
                1 -> ApiConfig.HuggingFace.DOCUMENT_OCR_MODEL
                2 -> ApiConfig.HuggingFace.GENERAL_VIT_MODEL
                3 -> ApiConfig.HuggingFace.GIT_MODEL
                4 -> ApiConfig.HuggingFace.CHESS_BOARD_SEGMENTATION
                5 -> ApiConfig.HuggingFace.CHESS_PIECE_DETECTION
                else -> ApiConfig.HuggingFace.IMAGE_CAPTIONING_MODEL
            }
            editor.putString(KEY_HF_MODEL, selectedModel)
        }

        editor.apply()

        // Apply settings to ApiConfig
        applySettings()

        showToast("Settings saved successfully")
    }

    private fun applySettings() {
        val providerOrdinal = prefs.getInt(KEY_API_PROVIDER, ApiConfig.ApiProvider.HUGGING_FACE.ordinal)
        ApiConfig.currentProvider = ApiConfig.ApiProvider.values()[providerOrdinal]

        if (ApiConfig.currentProvider == ApiConfig.ApiProvider.HUGGING_FACE) {
            ApiConfig.HuggingFace.apiToken = prefs.getString(KEY_HF_TOKEN, "") ?: ""
            ApiConfig.HuggingFace.currentModel = prefs.getString(KEY_HF_MODEL, ApiConfig.HuggingFace.IMAGE_CAPTIONING_MODEL) ?: ApiConfig.HuggingFace.IMAGE_CAPTIONING_MODEL
        }
    }

    private fun testConnection() {
        // Apply current settings temporarily
        applySettings()

        if (!ApiConfig.isConfigurationValid()) {
            showToast("Please configure your settings first")
            return
        }

        showToast("Connection test not implemented yet. Settings will be validated when you use the camera.")
    }

    private fun showToast(message: String) {
        Toast.makeText(this, message, Toast.LENGTH_SHORT).show()
    }

    override fun onSupportNavigateUp(): Boolean {
        finish()
        return true
    }

    /**
     * Static method to load settings on app startup
     */
    companion object {
        fun loadSettingsOnStartup(context: Context) {
            val prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)

            val providerOrdinal = prefs.getInt(KEY_API_PROVIDER, ApiConfig.ApiProvider.HUGGING_FACE.ordinal)
            ApiConfig.currentProvider = ApiConfig.ApiProvider.values().getOrNull(providerOrdinal) ?: ApiConfig.ApiProvider.HUGGING_FACE

            if (ApiConfig.currentProvider == ApiConfig.ApiProvider.HUGGING_FACE) {
                ApiConfig.HuggingFace.apiToken = prefs.getString(KEY_HF_TOKEN, "") ?: ""
                ApiConfig.HuggingFace.currentModel = prefs.getString(KEY_HF_MODEL, ApiConfig.HuggingFace.IMAGE_CAPTIONING_MODEL) ?: ApiConfig.HuggingFace.IMAGE_CAPTIONING_MODEL
            }
        }
    }
}
