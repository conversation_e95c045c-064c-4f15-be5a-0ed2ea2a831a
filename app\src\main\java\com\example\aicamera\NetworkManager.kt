package com.example.aicamera

import android.util.Log
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.MultipartBody
import okhttp3.OkHttpClient
import okhttp3.RequestBody
import okhttp3.RequestBody.Companion.asRequestBody
import okhttp3.RequestBody.Companion.toRequestBody
import okhttp3.logging.HttpLoggingInterceptor
import retrofit2.Response
import retrofit2.Retrofit
import retrofit2.converter.gson.GsonConverterFactory
import java.io.File
import java.text.SimpleDateFormat
import java.util.*
import java.util.concurrent.TimeUnit

/**
 * NetworkManager handles all network operations for communicating with AI servers
 * Supports both local server and Hugging Face cloud API
 */
class NetworkManager {

    companion object {
        private const val TAG = "NetworkManager"
        private const val FILENAME_FORMAT = "yyyy-MM-dd-HH-mm-ss-SSS"
    }

    private val apiService: ApiService
    private val imageOptimizer = ImageOptimizer()

    init {
        // Create HTTP client with logging and timeout configuration
        val loggingInterceptor = HttpLoggingInterceptor().apply {
            level = HttpLoggingInterceptor.Level.HEADERS // Reduced logging for cloud APIs
        }

        val httpClient = OkHttpClient.Builder()
            .addInterceptor(loggingInterceptor)
            .connectTimeout(ApiConfig.Network.CONNECT_TIMEOUT_SECONDS, TimeUnit.SECONDS)
            .readTimeout(ApiConfig.Network.READ_TIMEOUT_SECONDS, TimeUnit.SECONDS)
            .writeTimeout(ApiConfig.Network.WRITE_TIMEOUT_SECONDS, TimeUnit.SECONDS)
            .build()

        // Create Retrofit instance with dynamic base URL
        val retrofit = Retrofit.Builder()
            .baseUrl(ApiConfig.getBaseUrl())
            .client(httpClient)
            .addConverterFactory(GsonConverterFactory.create())
            .build()

        apiService = retrofit.create(ApiService::class.java)
    }

    /**
     * Upload an image file to the AI server (supports both local and cloud APIs)
     * @param imageFile The image file to upload
     * @return NetworkResult containing the response or error
     */
    suspend fun uploadImage(imageFile: File): NetworkResult<AiResponse> {
        // Validate configuration
        if (!ApiConfig.isConfigurationValid()) {
            return NetworkResult.Error("API configuration is invalid. Please check your settings.")
        }

        return when (ApiConfig.currentProvider) {
            ApiConfig.ApiProvider.LOCAL_SERVER -> uploadToLocalServer(imageFile)
            ApiConfig.ApiProvider.HUGGING_FACE -> uploadToHuggingFace(imageFile)
        }
    }

    /**
     * Upload image to local server
     */
    private suspend fun uploadToLocalServer(imageFile: File): NetworkResult<AiResponse> {
        return try {
            Log.d(TAG, "Uploading to local server: ${imageFile.name}")

            // Create request body from file
            val requestFile = imageFile.asRequestBody("image/*".toMediaTypeOrNull())
            val imagePart = MultipartBody.Part.createFormData("image", imageFile.name, requestFile)

            // Make API call
            val response: Response<LocalAiResponse> = apiService.uploadImageToLocalServer(imagePart)

            if (response.isSuccessful) {
                val localResponse = response.body()
                if (localResponse != null) {
                    // Convert to unified response format
                    val aiResponse = AiResponse(
                        success = localResponse.success,
                        message = localResponse.message,
                        result = localResponse.result,
                        error = localResponse.error,
                        provider = "local_server"
                    )
                    NetworkResult.Success(aiResponse)
                } else {
                    NetworkResult.Error("Empty response from local server")
                }
            } else {
                NetworkResult.Error("Local server error: ${response.code()} ${response.message()}")
            }

        } catch (e: Exception) {
            Log.e(TAG, "Local server upload failed", e)
            NetworkResult.Error("Local server error: ${e.message}")
        }
    }

    /**
     * Upload image to Hugging Face inference API
     */
    private suspend fun uploadToHuggingFace(imageFile: File): NetworkResult<AiResponse> {
        return try {
            Log.d(TAG, "Uploading to Hugging Face: ${imageFile.name}")

            // Optimize image for cloud API if needed
            val optimizedFile = if (imageOptimizer.needsOptimization(imageFile)) {
                val optimizedFile = createOptimizedFile()
                if (imageOptimizer.optimizeForCloudApi(imageFile, optimizedFile)) {
                    Log.d(TAG, "Image optimized: ${imageOptimizer.getFileSizeMB(imageFile):.2f}MB -> ${imageOptimizer.getFileSizeMB(optimizedFile):.2f}MB")
                    optimizedFile
                } else {
                    Log.w(TAG, "Image optimization failed, using original")
                    imageFile
                }
            } else {
                imageFile
            }

            // Convert image to byte array
            val imageBytes = imageOptimizer.imageToByteArray(optimizedFile)
            if (imageBytes == null) {
                return NetworkResult.Error("Failed to read image file")
            }

            // Create request body
            val requestBody = imageBytes.toRequestBody("image/jpeg".toMediaTypeOrNull())

            // Prepare authorization header
            val authHeader = "Bearer ${ApiConfig.HuggingFace.apiToken}"

            // Make API call
            val response: Response<List<HuggingFaceResponse>> = apiService.sendImageToHuggingFace(
                modelPath = ApiConfig.getEndpoint(),
                authorization = authHeader,
                imageData = requestBody
            )

            // Clean up optimized file if it was created
            if (optimizedFile != imageFile) {
                optimizedFile.delete()
            }

            if (response.isSuccessful) {
                val hfResponses = response.body()
                if (!hfResponses.isNullOrEmpty()) {
                    // Process Hugging Face response
                    val result = processHuggingFaceResponse(hfResponses)
                    val aiResponse = AiResponse(
                        success = true,
                        message = "Image processed successfully by ${ApiConfig.HuggingFace.currentModel}",
                        result = result.text,
                        confidence = result.confidence,
                        provider = "hugging_face"
                    )
                    NetworkResult.Success(aiResponse)
                } else {
                    NetworkResult.Error("Empty response from Hugging Face")
                }
            } else {
                val errorBody = response.errorBody()?.string()
                Log.e(TAG, "Hugging Face API error: ${response.code()} - $errorBody")

                val errorMessage = when (response.code()) {
                    401 -> "Invalid API token. Please check your Hugging Face credentials."
                    429 -> "Rate limit exceeded. Please try again later."
                    503 -> "Model is currently loading. Please try again in a few moments."
                    else -> "Hugging Face API error: ${response.code()} ${response.message()}"
                }

                NetworkResult.Error(errorMessage)
            }

        } catch (e: Exception) {
            Log.e(TAG, "Hugging Face upload failed", e)
            NetworkResult.Error("Hugging Face error: ${e.message}")
        }
    }

    /**
     * Process Hugging Face API response based on model type
     */
    private fun processHuggingFaceResponse(responses: List<HuggingFaceResponse>): ProcessedResponse {
        val firstResponse = responses.firstOrNull()

        return when {
            // Image captioning models
            firstResponse?.generated_text != null -> {
                ProcessedResponse(
                    text = firstResponse.generated_text,
                    confidence = firstResponse.score
                )
            }
            // Classification models
            firstResponse?.label != null -> {
                val labels = responses.mapNotNull { it.label }
                val scores = responses.mapNotNull { it.score }
                val result = if (labels.isNotEmpty()) {
                    "Classification: ${labels.joinToString(", ")}"
                } else {
                    "Classification completed"
                }
                ProcessedResponse(
                    text = result,
                    confidence = scores.maxOrNull()
                )
            }
            else -> {
                ProcessedResponse(
                    text = "Image processed successfully, but no text result available",
                    confidence = null
                )
            }
        }
    }

    /**
     * Create temporary file for optimized image
     */
    private fun createOptimizedFile(): File {
        val name = SimpleDateFormat(FILENAME_FORMAT, Locale.US).format(System.currentTimeMillis())
        return File.createTempFile("optimized_$name", ".jpg")
    }

    /**
     * Data class for processed response
     */
    private data class ProcessedResponse(
        val text: String,
        val confidence: Double?
    )
}

/**
 * Sealed class representing network operation results
 */
sealed class NetworkResult<T> {
    data class Success<T>(val data: T) : NetworkResult<T>()
    data class Error<T>(val message: String) : NetworkResult<T>()
    data class Loading<T>(val message: String = "Loading...") : NetworkResult<T>()
}
