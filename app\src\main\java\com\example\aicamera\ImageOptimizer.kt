package com.example.aicamera

import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.util.Log
import java.io.ByteArrayOutputStream
import java.io.File
import java.io.FileOutputStream
import kotlin.math.min

/**
 * Utility class for optimizing images for cloud API processing
 */
class ImageOptimizer {
    
    companion object {
        private const val TAG = "ImageOptimizer"
        private const val MAX_FILE_SIZE_MB = 5
        private const val MAX_FILE_SIZE_BYTES = MAX_FILE_SIZE_MB * 1024 * 1024
        private const val RECOMMENDED_MAX_DIMENSION = 1024
        private const val INITIAL_QUALITY = 90
        private const val MIN_QUALITY = 60
    }
    
    /**
     * Optimize image for cloud API processing
     * @param inputFile Original image file
     * @param outputFile Output file for optimized image
     * @param maxDimension Maximum width/height (default: 1024)
     * @return True if optimization successful, false otherwise
     */
    fun optimizeForCloudApi(
        inputFile: File,
        outputFile: File,
        maxDimension: Int = RECOMMENDED_MAX_DIMENSION
    ): Bo<PERSON>an {
        return try {
            // Load original bitmap
            val originalBitmap = BitmapFactory.decodeFile(inputFile.absolutePath)
            if (originalBitmap == null) {
                Log.e(TAG, "Failed to decode input image")
                return false
            }
            
            // Calculate new dimensions
            val (newWidth, newHeight) = calculateOptimalDimensions(
                originalBitmap.width,
                originalBitmap.height,
                maxDimension
            )
            
            // Resize bitmap if needed
            val resizedBitmap = if (newWidth != originalBitmap.width || newHeight != originalBitmap.height) {
                Bitmap.createScaledBitmap(originalBitmap, newWidth, newHeight, true)
            } else {
                originalBitmap
            }
            
            // Compress to meet file size requirements
            val success = compressToFileSize(resizedBitmap, outputFile, MAX_FILE_SIZE_BYTES)
            
            // Clean up bitmaps
            if (resizedBitmap != originalBitmap) {
                resizedBitmap.recycle()
            }
            originalBitmap.recycle()
            
            if (success) {
                Log.d(TAG, "Image optimized: ${inputFile.length()} -> ${outputFile.length()} bytes")
            }
            
            success
            
        } catch (e: Exception) {
            Log.e(TAG, "Error optimizing image", e)
            false
        }
    }
    
    /**
     * Calculate optimal dimensions while maintaining aspect ratio
     */
    private fun calculateOptimalDimensions(
        originalWidth: Int,
        originalHeight: Int,
        maxDimension: Int
    ): Pair<Int, Int> {
        if (originalWidth <= maxDimension && originalHeight <= maxDimension) {
            return Pair(originalWidth, originalHeight)
        }
        
        val aspectRatio = originalWidth.toFloat() / originalHeight.toFloat()
        
        return if (originalWidth > originalHeight) {
            // Landscape: limit width
            val newWidth = maxDimension
            val newHeight = (newWidth / aspectRatio).toInt()
            Pair(newWidth, newHeight)
        } else {
            // Portrait: limit height
            val newHeight = maxDimension
            val newWidth = (newHeight * aspectRatio).toInt()
            Pair(newWidth, newHeight)
        }
    }
    
    /**
     * Compress bitmap to meet file size requirements
     */
    private fun compressToFileSize(
        bitmap: Bitmap,
        outputFile: File,
        maxSizeBytes: Int
    ): Boolean {
        var quality = INITIAL_QUALITY
        
        while (quality >= MIN_QUALITY) {
            val outputStream = ByteArrayOutputStream()
            bitmap.compress(Bitmap.CompressFormat.JPEG, quality, outputStream)
            val compressedData = outputStream.toByteArray()
            
            if (compressedData.size <= maxSizeBytes) {
                // Size is acceptable, save to file
                return try {
                    FileOutputStream(outputFile).use { fileOut ->
                        fileOut.write(compressedData)
                    }
                    Log.d(TAG, "Compressed to ${compressedData.size} bytes at quality $quality")
                    true
                } catch (e: Exception) {
                    Log.e(TAG, "Error saving compressed image", e)
                    false
                }
            }
            
            quality -= 10
        }
        
        Log.e(TAG, "Could not compress image to required size")
        return false
    }
    
    /**
     * Get image file size in MB
     */
    fun getFileSizeMB(file: File): Double {
        return file.length().toDouble() / (1024 * 1024)
    }
    
    /**
     * Check if image needs optimization for cloud API
     */
    fun needsOptimization(file: File, maxDimension: Int = RECOMMENDED_MAX_DIMENSION): Boolean {
        // Check file size
        if (file.length() > MAX_FILE_SIZE_BYTES) {
            return true
        }
        
        // Check dimensions
        val options = BitmapFactory.Options().apply {
            inJustDecodeBounds = true
        }
        BitmapFactory.decodeFile(file.absolutePath, options)
        
        return options.outWidth > maxDimension || options.outHeight > maxDimension
    }
    
    /**
     * Convert image to byte array for API request
     */
    fun imageToByteArray(file: File): ByteArray? {
        return try {
            file.readBytes()
        } catch (e: Exception) {
            Log.e(TAG, "Error reading image file", e)
            null
        }
    }
    
    /**
     * Get image dimensions without loading full bitmap
     */
    fun getImageDimensions(file: File): Pair<Int, Int>? {
        return try {
            val options = BitmapFactory.Options().apply {
                inJustDecodeBounds = true
            }
            BitmapFactory.decodeFile(file.absolutePath, options)
            Pair(options.outWidth, options.outHeight)
        } catch (e: Exception) {
            Log.e(TAG, "Error getting image dimensions", e)
            null
        }
    }
}
