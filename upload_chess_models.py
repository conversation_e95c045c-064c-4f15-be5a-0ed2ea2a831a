#!/usr/bin/env python3
"""
Upload Chess AI Models to Hugging Face Hub
Corrected version with actual file paths and configurations
"""

import os
import json
import shutil
from huggingface_hub import HfA<PERSON>, create_repo

# Your Hugging Face token
HF_TOKEN = "*************************************"

def upload_chess_board_segmentation(username):
    """Upload V6 Chess Board Segmentation Model"""
    print("\n🚀 Uploading Chess Board Segmentation V6...")
    
    repo_name = f"{username}/chess-board-segmentation-v6"
    model_path = r"C:\Users\<USER>\OneDrive\Desktop\fizzi chess\best_model.pth"
    
    # Check if model file exists
    if not os.path.exists(model_path):
        print(f"❌ Model file not found: {model_path}")
        return False
    
    # Create temporary directory
    temp_dir = "temp_segmentation"
    os.makedirs(temp_dir, exist_ok=True)
    
    try:
        # Copy model file
        shutil.copy2(model_path, os.path.join(temp_dir, "pytorch_model.bin"))
        
        # Create config.json
        config = {
            "model_type": "unet",
            "task": "image-segmentation",
            "architectures": ["BreakthroughUNetV6"],
            "base_channels": 32,
            "input_size": [256, 256],
            "num_classes": 1,
            "dice_score": 0.9391,
            "license": "apache-2.0",
            "tags": ["chess", "segmentation", "unet", "computer-vision"]
        }
        
        with open(os.path.join(temp_dir, "config.json"), 'w') as f:
            json.dump(config, f, indent=2)
        
        # Create README.md
        readme = f"""---
license: apache-2.0
tags:
- chess
- segmentation
- unet
- computer-vision
pipeline_tag: image-segmentation
---

# Chess Board Segmentation V6

## Model Description
Breakthrough V6 UNet model for precise chess board segmentation and boundary detection.

## Performance
- **Dice Score**: 0.9391
- **Input Size**: 256x256
- **Architecture**: Custom UNet V6 with 32 base channels

## Usage
```python
import torch
from transformers import AutoModel

model = AutoModel.from_pretrained("{username}/chess-board-segmentation-v6")
# Process 256x256 RGB images for chess board detection
```

## Training
Trained on diverse chess board images with various angles, lighting conditions, and board styles.
Achieves breakthrough performance with 0.9391 Dice score for precise board boundary detection.

## Applications
- Chess board detection in images
- Perspective correction for chess analysis
- Geometric grid creation for piece detection
- FEN generation pipeline preprocessing
"""
        
        with open(os.path.join(temp_dir, "README.md"), 'w') as f:
            f.write(readme)
        
        # Create repository and upload
        print(f"📦 Creating repository: {repo_name}")
        create_repo(repo_name, token=HF_TOKEN, exist_ok=True)
        
        api = HfApi()
        
        # Upload files
        for filename in ["pytorch_model.bin", "config.json", "README.md"]:
            file_path = os.path.join(temp_dir, filename)
            print(f"📤 Uploading {filename}...")
            api.upload_file(
                path_or_fileobj=file_path,
                path_in_repo=filename,
                repo_id=repo_name,
                token=HF_TOKEN
            )
            print(f"✅ Uploaded {filename}")
        
        print(f"🎉 Successfully uploaded Chess Board Segmentation V6!")
        print(f"🔗 Model URL: https://huggingface.co/{repo_name}")
        return True
        
    except Exception as e:
        print(f"❌ Upload failed: {e}")
        return False
    finally:
        if os.path.exists(temp_dir):
            shutil.rmtree(temp_dir)

def upload_chess_piece_detection(username):
    """Upload YOLO Chess Piece Detection Model"""
    print("\n🚀 Uploading Chess Piece Detection YOLO...")
    
    repo_name = f"{username}/chess-piece-detection-yolo11n"
    model_path = r"C:\Users\<USER>\OneDrive\Desktop\fizzi chess\best.pt"
    
    # Check if model file exists
    if not os.path.exists(model_path):
        print(f"❌ Model file not found: {model_path}")
        return False
    
    # Create temporary directory
    temp_dir = "temp_pieces"
    os.makedirs(temp_dir, exist_ok=True)
    
    try:
        # Copy model file
        shutil.copy2(model_path, os.path.join(temp_dir, "pytorch_model.bin"))
        
        # Create config.json
        config = {
            "model_type": "yolo",
            "task": "object-detection",
            "architectures": ["YOLO11n"],
            "input_size": [416, 416],
            "num_classes": 12,
            "class_names": [
                "white_pawn", "white_knight", "white_bishop", "white_rook", "white_queen", "white_king",
                "black_pawn", "black_knight", "black_bishop", "black_rook", "black_queen", "black_king"
            ],
            "license": "apache-2.0",
            "tags": ["chess", "object-detection", "yolo", "pieces"]
        }
        
        with open(os.path.join(temp_dir, "config.json"), 'w') as f:
            json.dump(config, f, indent=2)
        
        # Create README.md
        readme = f"""---
license: apache-2.0
tags:
- chess
- object-detection
- yolo
- pieces
pipeline_tag: object-detection
---

# Chess Piece Detection YOLO11n

## Model Description
YOLO11n model optimized for detecting and classifying chess pieces on a board.

## Classes
- **White pieces**: Pawn, Knight, Bishop, Rook, Queen, King
- **Black pieces**: pawn, knight, bishop, rook, queen, king

## Performance
- **Input Size**: 416x416
- **Architecture**: YOLO11n
- **Optimized**: Enhanced GPU training with smart filtering

## Usage
```python
from ultralytics import YOLO

model = YOLO("{username}/chess-piece-detection-yolo11n")
results = model("chess_board.jpg", imgsz=416, conf=0.5)
```

## Training
Trained on diverse chess piece datasets with various board styles, lighting, and angles.
Includes smart filtering to remove board markings and false positives.

## Applications
- Chess piece detection and classification
- FEN notation generation
- Chess position analysis
- Automated chess game recording
"""
        
        with open(os.path.join(temp_dir, "README.md"), 'w') as f:
            f.write(readme)
        
        # Create repository and upload
        print(f"📦 Creating repository: {repo_name}")
        create_repo(repo_name, token=HF_TOKEN, exist_ok=True)
        
        api = HfApi()
        
        # Upload files
        for filename in ["pytorch_model.bin", "config.json", "README.md"]:
            file_path = os.path.join(temp_dir, filename)
            print(f"📤 Uploading {filename}...")
            api.upload_file(
                path_or_fileobj=file_path,
                path_in_repo=filename,
                repo_id=repo_name,
                token=HF_TOKEN
            )
            print(f"✅ Uploaded {filename}")
        
        print(f"🎉 Successfully uploaded Chess Piece Detection YOLO!")
        print(f"🔗 Model URL: https://huggingface.co/{repo_name}")
        return True
        
    except Exception as e:
        print(f"❌ Upload failed: {e}")
        return False
    finally:
        if os.path.exists(temp_dir):
            shutil.rmtree(temp_dir)

def main():
    """Main upload function"""
    print("🤗 Chess AI Models Upload to Hugging Face")
    print("=" * 50)
    print(f"🔑 Token: {HF_TOKEN[:10]}...")
    
    # Get username
    username = input("Enter your Hugging Face username: ").strip()
    if not username:
        print("❌ Username required")
        return
    
    print(f"👤 Username: {username}")
    
    # Upload models
    success_count = 0
    
    if upload_chess_board_segmentation(username):
        success_count += 1
    
    if upload_chess_piece_detection(username):
        success_count += 1
    
    print(f"\n🎯 Upload Summary:")
    print(f"✅ Successful: {success_count}/2")
    
    if success_count == 2:
        print("\n🎉 All chess models uploaded successfully!")
        print(f"\n📱 Update your Android app ApiConfig.kt:")
        print(f'const val CHESS_BOARD_SEGMENTATION = "{username}/chess-board-segmentation-v6"')
        print(f'const val CHESS_PIECE_DETECTION = "{username}/chess-piece-detection-yolo11n"')
        print("\n🔗 Your models:")
        print(f"• https://huggingface.co/{username}/chess-board-segmentation-v6")
        print(f"• https://huggingface.co/{username}/chess-piece-detection-yolo11n")
    else:
        print("\n⚠️ Some uploads failed. Check the error messages above.")

if __name__ == "__main__":
    main()
