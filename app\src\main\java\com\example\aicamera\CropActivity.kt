package com.example.aicamera

import android.app.Activity
import android.content.Intent
import android.graphics.BitmapFactory
import android.graphics.PointF
import android.os.Bundle
import android.util.Log
import android.view.View
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.lifecycleScope
import com.bumptech.glide.Glide
import com.example.aicamera.databinding.ActivityCropBinding
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.io.File
import java.text.SimpleDateFormat
import java.util.*

class CropActivity : AppCompatActivity() {
    
    companion object {
        const val EXTRA_IMAGE_PATH = "extra_image_path"
        const val EXTRA_CROPPED_IMAGE_PATH = "extra_cropped_image_path"
        private const val TAG = "CropActivity"
        private const val FILENAME_FORMAT = "yyyy-MM-dd-HH-mm-ss-SSS"
    }
    
    private lateinit var binding: ActivityCropBinding
    private lateinit var perspectiveTransform: PerspectiveTransform
    private var inputImageFile: File? = null
    private var imageWidth = 0
    private var imageHeight = 0
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityCropBinding.inflate(layoutInflater)
        setContentView(binding.root)
        
        // Initialize perspective transform
        perspectiveTransform = PerspectiveTransform()
        
        // Set up toolbar
        setSupportActionBar(binding.toolbar)
        supportActionBar?.setDisplayHomeAsUpEnabled(true)
        
        // Get image path from intent
        val imagePath = intent.getStringExtra(EXTRA_IMAGE_PATH)
        if (imagePath == null) {
            showError("No image path provided")
            finish()
            return
        }
        
        inputImageFile = File(imagePath)
        if (!inputImageFile!!.exists()) {
            showError("Image file not found")
            finish()
            return
        }
        
        // Initialize OpenCV
        initializeOpenCV()
        
        // Set up UI
        setupUI()
        
        // Load image
        loadImage()
    }
    
    private fun initializeOpenCV() {
        PerspectiveTransform.initializeOpenCV { success ->
            if (!success) {
                showError("Failed to initialize image processing")
                finish()
            }
        }
    }
    
    private fun setupUI() {
        binding.cancelButton.setOnClickListener {
            setResult(Activity.RESULT_CANCELED)
            finish()
        }
        
        binding.resetButton.setOnClickListener {
            binding.cropView.resetToDefault()
        }
        
        binding.cropButton.setOnClickListener {
            performCrop()
        }
        
        // Set up crop view callback
        binding.cropView.onCornersChanged = { corners ->
            // Could add real-time feedback here if needed
            Log.d(TAG, "Corners changed: $corners")
        }
    }
    
    private fun loadImage() {
        inputImageFile?.let { file ->
            // Load image with Glide for efficient memory management
            Glide.with(this)
                .load(file)
                .into(binding.imageView)
            
            // Get image dimensions
            val options = BitmapFactory.Options().apply {
                inJustDecodeBounds = true
            }
            BitmapFactory.decodeFile(file.absolutePath, options)
            imageWidth = options.outWidth
            imageHeight = options.outHeight
            
            Log.d(TAG, "Image loaded: ${imageWidth}x${imageHeight}")
        }
    }
    
    private fun performCrop() {
        val inputFile = inputImageFile ?: return
        
        // Show progress
        showProgress(true)
        updateStatus(getString(R.string.cropping_image))
        
        lifecycleScope.launch {
            try {
                val success = withContext(Dispatchers.IO) {
                    // Get corner points in image coordinates
                    val corners = binding.cropView.getImageCorners(imageWidth, imageHeight)
                    
                    // Order points properly for perspective transformation
                    val orderedCorners = perspectiveTransform.orderPoints(corners)
                    
                    // Create output file
                    val outputFile = createOutputFile()
                    
                    // Perform perspective transformation
                    perspectiveTransform.transformQuadrilateral(
                        inputFile = inputFile,
                        outputFile = outputFile,
                        corners = orderedCorners
                    )
                }
                
                if (success) {
                    // Return result
                    val outputFile = createOutputFile()
                    val resultIntent = Intent().apply {
                        putExtra(EXTRA_CROPPED_IMAGE_PATH, outputFile.absolutePath)
                    }
                    setResult(Activity.RESULT_OK, resultIntent)
                    showToast(getString(R.string.crop_success))
                    finish()
                } else {
                    showError(getString(R.string.crop_failed))
                }
                
            } catch (e: Exception) {
                Log.e(TAG, "Error during cropping", e)
                showError("Cropping failed: ${e.message}")
            } finally {
                showProgress(false)
            }
        }
    }
    
    private fun createOutputFile(): File {
        val name = SimpleDateFormat(FILENAME_FORMAT, Locale.US)
            .format(System.currentTimeMillis())
        return File(getExternalFilesDir(null), "cropped_$name.jpg")
    }
    
    private fun showProgress(show: Boolean) {
        binding.progressBar.visibility = if (show) View.VISIBLE else View.GONE
        binding.cropButton.isEnabled = !show
        binding.resetButton.isEnabled = !show
    }
    
    private fun updateStatus(message: String) {
        binding.statusText.text = message
        binding.statusText.visibility = View.VISIBLE
    }
    
    private fun showError(message: String) {
        updateStatus("Error: $message")
        showToast(message)
        Log.e(TAG, message)
    }
    
    private fun showToast(message: String) {
        Toast.makeText(this, message, Toast.LENGTH_SHORT).show()
    }
    
    override fun onSupportNavigateUp(): Boolean {
        setResult(Activity.RESULT_CANCELED)
        finish()
        return true
    }
}
