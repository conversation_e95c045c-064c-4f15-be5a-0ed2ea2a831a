package com.example.aicamera

import android.content.Context
import android.graphics.*
import android.util.AttributeSet
import android.view.MotionEvent
import android.view.View
import kotlin.math.abs
import kotlin.math.sqrt

/**
 * Custom view for quadrilateral cropping with draggable corner points
 */
class QuadrilateralCropView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : View(context, attrs, defStyleAttr) {
    
    companion object {
        private const val CORNER_RADIUS = 30f
        private const val TOUCH_TOLERANCE = 50f
        private const val STROKE_WIDTH = 4f
        private const val OVERLAY_ALPHA = 100
    }
    
    // Corner points of the quadrilateral (in view coordinates)
    private val corners = mutableListOf<PointF>()
    
    // Paint objects for drawing
    private val linePaint = Paint().apply {
        color = Color.BLUE
        strokeWidth = STROKE_WIDTH
        style = Paint.Style.STROKE
        isAntiAlias = true
    }
    
    private val cornerPaint = Paint().apply {
        color = Color.BLUE
        style = Paint.Style.FILL
        isAntiAlias = true
    }
    
    private val overlayPaint = Paint().apply {
        color = Color.BLACK
        alpha = OVERLAY_ALPHA
        style = Paint.Style.FILL
    }
    
    private val cropAreaPaint = Paint().apply {
        color = Color.TRANSPARENT
        style = Paint.Style.FILL
        xfermode = PorterDuffXfermode(PorterDuff.Mode.CLEAR)
    }
    
    // Touch handling
    private var draggedCornerIndex = -1
    private var lastTouchX = 0f
    private var lastTouchY = 0f
    
    // Callback for corner changes
    var onCornersChanged: ((List<PointF>) -> Unit)? = null
    
    init {
        // Initialize with default rectangle covering most of the view
        initializeDefaultCorners()
    }
    
    private fun initializeDefaultCorners() {
        // Will be properly set when view is measured
        corners.clear()
        corners.addAll(listOf(
            PointF(100f, 100f),      // top-left
            PointF(300f, 100f),      // top-right
            PointF(300f, 300f),      // bottom-right
            PointF(100f, 300f)       // bottom-left
        ))
    }
    
    override fun onSizeChanged(w: Int, h: Int, oldw: Int, oldh: Int) {
        super.onSizeChanged(w, h, oldw, oldh)
        
        // Initialize corners to cover most of the view with some margin
        val margin = 50f
        corners.clear()
        corners.addAll(listOf(
            PointF(margin, margin),                    // top-left
            PointF(w - margin, margin),                // top-right
            PointF(w - margin, h - margin),            // bottom-right
            PointF(margin, h - margin)                 // bottom-left
        ))
        
        notifyCornersChanged()
    }
    
    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)
        
        if (corners.size != 4) return
        
        // Draw overlay (darkened area outside crop region)
        drawOverlay(canvas)
        
        // Draw quadrilateral lines
        drawQuadrilateral(canvas)
        
        // Draw corner points
        drawCorners(canvas)
    }
    
    private fun drawOverlay(canvas: Canvas) {
        // Create a path for the crop area
        val cropPath = Path().apply {
            moveTo(corners[0].x, corners[0].y)
            lineTo(corners[1].x, corners[1].y)
            lineTo(corners[2].x, corners[2].y)
            lineTo(corners[3].x, corners[3].y)
            close()
        }
        
        // Draw overlay covering the entire view
        canvas.drawRect(0f, 0f, width.toFloat(), height.toFloat(), overlayPaint)
        
        // Clear the crop area (make it transparent)
        canvas.drawPath(cropPath, cropAreaPaint)
    }
    
    private fun drawQuadrilateral(canvas: Canvas) {
        // Draw lines connecting the corners
        for (i in corners.indices) {
            val start = corners[i]
            val end = corners[(i + 1) % corners.size]
            canvas.drawLine(start.x, start.y, end.x, end.y, linePaint)
        }
    }
    
    private fun drawCorners(canvas: Canvas) {
        corners.forEach { corner ->
            canvas.drawCircle(corner.x, corner.y, CORNER_RADIUS, cornerPaint)
        }
    }
    
    override fun onTouchEvent(event: MotionEvent): Boolean {
        when (event.action) {
            MotionEvent.ACTION_DOWN -> {
                lastTouchX = event.x
                lastTouchY = event.y
                draggedCornerIndex = findNearestCorner(event.x, event.y)
                return draggedCornerIndex != -1
            }
            
            MotionEvent.ACTION_MOVE -> {
                if (draggedCornerIndex != -1) {
                    val dx = event.x - lastTouchX
                    val dy = event.y - lastTouchY
                    
                    // Update corner position
                    val corner = corners[draggedCornerIndex]
                    corner.x = (corner.x + dx).coerceIn(0f, width.toFloat())
                    corner.y = (corner.y + dy).coerceIn(0f, height.toFloat())
                    
                    lastTouchX = event.x
                    lastTouchY = event.y
                    
                    invalidate()
                    notifyCornersChanged()
                    return true
                }
            }
            
            MotionEvent.ACTION_UP -> {
                draggedCornerIndex = -1
                return true
            }
        }
        
        return super.onTouchEvent(event)
    }
    
    private fun findNearestCorner(x: Float, y: Float): Int {
        var nearestIndex = -1
        var minDistance = Float.MAX_VALUE
        
        corners.forEachIndexed { index, corner ->
            val distance = sqrt((x - corner.x) * (x - corner.x) + (y - corner.y) * (y - corner.y))
            if (distance < TOUCH_TOLERANCE && distance < minDistance) {
                minDistance = distance
                nearestIndex = index
            }
        }
        
        return nearestIndex
    }
    
    /**
     * Get the current corner points
     */
    fun getCorners(): List<PointF> {
        return corners.map { PointF(it.x, it.y) }
    }
    
    /**
     * Set corner points programmatically
     */
    fun setCorners(newCorners: List<PointF>) {
        if (newCorners.size == 4) {
            corners.clear()
            corners.addAll(newCorners.map { PointF(it.x, it.y) })
            invalidate()
            notifyCornersChanged()
        }
    }
    
    /**
     * Convert view coordinates to image coordinates
     */
    fun getImageCorners(imageWidth: Int, imageHeight: Int): List<PointF> {
        val scaleX = imageWidth.toFloat() / width
        val scaleY = imageHeight.toFloat() / height
        
        return corners.map { corner ->
            PointF(corner.x * scaleX, corner.y * scaleY)
        }
    }
    
    /**
     * Set corners based on image coordinates
     */
    fun setImageCorners(imageCorners: List<PointF>, imageWidth: Int, imageHeight: Int) {
        if (imageCorners.size == 4 && width > 0 && height > 0) {
            val scaleX = width.toFloat() / imageWidth
            val scaleY = height.toFloat() / imageHeight
            
            val viewCorners = imageCorners.map { corner ->
                PointF(corner.x * scaleX, corner.y * scaleY)
            }
            
            setCorners(viewCorners)
        }
    }
    
    private fun notifyCornersChanged() {
        onCornersChanged?.invoke(getCorners())
    }
    
    /**
     * Reset corners to default rectangle
     */
    fun resetToDefault() {
        if (width > 0 && height > 0) {
            val margin = 50f
            setCorners(listOf(
                PointF(margin, margin),                    // top-left
                PointF(width - margin, margin),            // top-right
                PointF(width - margin, height - margin),   // bottom-right
                PointF(margin, height - margin)            // bottom-left
            ))
        }
    }
}
