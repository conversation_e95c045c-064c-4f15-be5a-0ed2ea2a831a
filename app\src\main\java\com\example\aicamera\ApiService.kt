package com.example.aicamera

import okhttp3.MultipartBody
import okhttp3.RequestBody
import okhttp3.ResponseBody
import retrofit2.Response
import retrofit2.http.*

/**
 * API service interface for communicating with AI servers (local and cloud)
 */
interface ApiService {

    /**
     * Upload an image to the local AI server for processing
     * @param image The image file as MultipartBody.Part
     * @return Response containing the AI model's response
     */
    @Multipart
    @POST("process-image")
    suspend fun uploadImageToLocalServer(
        @Part image: MultipartBody.Part
    ): Response<LocalAiResponse>

    /**
     * Send image to Hugging Face inference API
     * @param modelPath The model path (e.g., "models/Salesforce/blip-image-captioning-base")
     * @param authorization Bearer token for authentication
     * @param imageData Raw image data
     * @return Response containing the model's output
     */
    @POST
    suspend fun sendImageToHuggingFace(
        @Url modelPath: String,
        @Header("Authorization") authorization: String,
        @Body imageData: RequestBody
    ): Response<List<HuggingFaceResponse>>
}

/**
 * Data class representing the local AI server response
 */
data class LocalAiResponse(
    val success: Boolean,
    val message: String,
    val result: String? = null,
    val error: String? = null
)

/**
 * Data class representing Hugging Face API response
 */
data class HuggingFaceResponse(
    val generated_text: String? = null,
    val score: Double? = null,
    val label: String? = null
)

/**
 * Unified response format for the app
 */
data class AiResponse(
    val success: Boolean,
    val message: String,
    val result: String? = null,
    val error: String? = null,
    val confidence: Double? = null,
    val provider: String = "unknown"
)
