package com.example.aicamera

import okhttp3.MultipartBody
import okhttp3.ResponseBody
import retrofit2.Response
import retrofit2.http.Multipart
import retrofit2.http.POST
import retrofit2.http.Part

/**
 * API service interface for communicating with the local AI server
 */
interface ApiService {
    
    /**
     * Upload an image to the AI server for processing
     * @param image The image file as MultipartBody.Part
     * @return Response containing the AI model's response
     */
    @Multipart
    @POST("process-image")
    suspend fun uploadImage(
        @Part image: MultipartBody.Part
    ): Response<AiResponse>
}

/**
 * Data class representing the AI server response
 */
data class AiResponse(
    val success: Boolean,
    val message: String,
    val result: String? = null,
    val error: String? = null
)
