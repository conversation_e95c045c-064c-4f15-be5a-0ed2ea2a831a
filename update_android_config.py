#!/usr/bin/env python3
"""
Update Android ApiConfig.kt with actual Hugging Face username
Run this after successful model upload
"""

import re

def update_api_config(username):
    """Update ApiConfig.kt with actual username"""
    
    config_path = "app/src/main/java/com/example/aicamera/ApiConfig.kt"
    
    try:
        # Read current content
        with open(config_path, 'r') as f:
            content = f.read()
        
        # Replace placeholders with actual username
        updated_content = content.replace(
            'const val CHESS_BOARD_SEGMENTATION = "your-username/chess-board-segmentation-v6"',
            f'const val CHESS_BOARD_SEGMENTATION = "{username}/chess-board-segmentation-v6"'
        )
        
        updated_content = updated_content.replace(
            'const val CHESS_PIECE_DETECTION = "your-username/chess-piece-detection-yolo11n"',
            f'const val CHESS_PIECE_DETECTION = "{username}/chess-piece-detection-yolo11n"'
        )
        
        # Write updated content
        with open(config_path, 'w') as f:
            f.write(updated_content)
        
        print(f"✅ Updated ApiConfig.kt with username: {username}")
        print(f"🔗 Chess Board Segmentation: {username}/chess-board-segmentation-v6")
        print(f"🔗 Chess Piece Detection: {username}/chess-piece-detection-yolo11n")
        
        return True
        
    except Exception as e:
        print(f"❌ Failed to update ApiConfig.kt: {e}")
        return False

def main():
    username = input("Enter your Hugging Face username: ").strip()
    if username:
        update_api_config(username)
    else:
        print("❌ Username required")

if __name__ == "__main__":
    main()
