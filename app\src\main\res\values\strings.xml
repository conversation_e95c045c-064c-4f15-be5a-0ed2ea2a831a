<resources>
    <string name="app_name">AI Camera</string>
    <string name="capture_photo">Capture Photo</string>
    <string name="send_to_ai">Send to AI</string>
    <string name="ai_response">AI Response</string>
    <string name="camera_permission_required">Camera permission is required to capture photos</string>
    <string name="network_error">Network error occurred</string>
    <string name="upload_success">Image uploaded successfully</string>
    <string name="upload_failed">Failed to upload image</string>
    <string name="processing">Processing...</string>
    <string name="no_response">No response from AI</string>
    <string name="grant_permission">Grant Permission</string>
    <string name="retry">Retry</string>

    <!-- Crop Activity Strings -->
    <string name="crop_image">Crop Image</string>
    <string name="captured_image">Captured Image</string>
    <string name="cancel">Cancel</string>
    <string name="reset">Reset</string>
    <string name="crop_and_continue">Crop &amp; Continue</string>
    <string name="adjust_crop_area">Drag the corner points to adjust the crop area</string>
    <string name="cropping_image">Cropping image...</string>
    <string name="crop_failed">Failed to crop image</string>
    <string name="crop_success">Image cropped successfully</string>

    <!-- Settings Activity Strings -->
    <string name="settings">Settings</string>
    <string name="api_provider">AI Provider</string>
    <string name="api_provider_description">Choose between cloud-based Hugging Face API or local server</string>
    <string name="hugging_face_settings">Hugging Face Configuration</string>
    <string name="local_server_settings">Local Server Configuration</string>
    <string name="local_server_description">Local server configuration is handled in NetworkManager.kt</string>
    <string name="api_token">API Token</string>
    <string name="api_token_help">Get your free token from huggingface.co/settings/tokens</string>
    <string name="ai_model">AI Model</string>
    <string name="ai_model_description">Select the AI model for image processing</string>
    <string name="test_connection">Test Connection</string>
    <string name="save_settings">Save Settings</string>
    <string name="processing_with_cloud">Processing with cloud AI...</string>
    <string name="processing_with_local">Processing with local AI...</string>
</resources>
