<resources>
    <string name="app_name">AI Camera</string>
    <string name="capture_photo">Capture Photo</string>
    <string name="send_to_ai">Send to AI</string>
    <string name="ai_response">AI Response</string>
    <string name="camera_permission_required">Camera permission is required to capture photos</string>
    <string name="network_error">Network error occurred</string>
    <string name="upload_success">Image uploaded successfully</string>
    <string name="upload_failed">Failed to upload image</string>
    <string name="processing">Processing...</string>
    <string name="no_response">No response from AI</string>
    <string name="grant_permission">Grant Permission</string>
    <string name="retry">Retry</string>

    <!-- Crop Activity Strings -->
    <string name="crop_image">Crop Image</string>
    <string name="captured_image">Captured Image</string>
    <string name="cancel">Cancel</string>
    <string name="reset">Reset</string>
    <string name="crop_and_continue">Crop &amp; Continue</string>
    <string name="adjust_crop_area">Drag the corner points to adjust the crop area</string>
    <string name="cropping_image">Cropping image...</string>
    <string name="crop_failed">Failed to crop image</string>
    <string name="crop_success">Image cropped successfully</string>
</resources>
