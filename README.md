# AI Camera Android App

An Android application that captures photos using the device camera and sends them to a local AI server for processing.

## Features

- **Camera Integration**: Capture photos using CameraX library
- **Network Communication**: Send images to local AI server via REST API
- **Real-time Response**: Display AI model responses in the app
- **Error Handling**: Comprehensive error handling for camera and network operations
- **Permissions**: Proper handling of camera and network permissions

## Technical Specifications

- **Language**: Kotlin
- **Minimum SDK**: Android 9 (API level 28)
- **Target SDK**: Android 15 (API level 35)
- **Architecture**: Client-server with REST API communication
- **Camera Library**: CameraX
- **Network Library**: Retrofit + OkHttp

## Setup Instructions

### Prerequisites

1. **Android Studio**: Latest version with Android SDK
2. **Android Device/Emulator**: Running Android 9 or higher
3. **Local AI Server**: Running on your PC (see Server Setup below)

### Android App Setup

1. **Clone/Download** this project
2. **Open** in Android Studio
3. **Configure SDK Path**: Update `local.properties` with your Android SDK path:
   ```
   sdk.dir=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk
   ```
4. **Sync Project** with Gradle files
5. **Update Server URL**: In `NetworkManager.kt`, update the `BASE_URL` to match your PC's IP address:
   ```kotlin
   private const val BASE_URL = "http://YOUR_PC_IP:8000/"
   ```

### Server Setup

Your local AI server should expose a REST API endpoint:

**Endpoint**: `POST /process-image`
**Content-Type**: `multipart/form-data`
**Parameter**: `image` (file)

**Expected Response Format**:
```json
{
  "success": true,
  "message": "Image processed successfully",
  "result": "AI model response text here",
  "error": null
}
```

### Network Configuration

1. **Same Network**: Ensure your Android device and PC are on the same network
2. **Firewall**: Configure firewall to allow connections on your server port
3. **IP Address**: Find your PC's local IP address (usually 192.168.x.x)

## Usage

1. **Launch App**: Open the AI Camera app
2. **Grant Permissions**: Allow camera access when prompted
3. **Capture Photo**: Tap "Capture Photo" button
4. **Send to AI**: Tap "Send to AI" button to upload and process
5. **View Response**: AI response will appear in the text area below

## Project Structure

```
app/src/main/java/com/example/aicamera/
├── MainActivity.kt          # Main activity with UI logic
├── CameraManager.kt         # Camera operations using CameraX
├── NetworkManager.kt        # Network operations and API calls
└── ApiService.kt           # Retrofit API interface

app/src/main/res/
├── layout/
│   └── activity_main.xml   # Main UI layout
├── values/
│   ├── strings.xml         # String resources
│   ├── colors.xml          # Color definitions
│   └── themes.xml          # App themes
└── xml/                    # Backup and data extraction rules
```

## Key Components

### CameraManager
- Handles camera initialization and configuration
- Manages photo capture using CameraX
- Provides callbacks for success/error handling

### NetworkManager
- Manages HTTP client configuration
- Handles image upload to AI server
- Provides structured response handling

### MainActivity
- Coordinates camera and network operations
- Manages UI state and user interactions
- Handles permissions and error display

## Troubleshooting

### Common Issues

1. **Camera Permission Denied**: Grant camera permission in app settings
2. **Network Error**: Check if server is running and accessible
3. **Connection Timeout**: Verify IP address and port in NetworkManager
4. **Build Errors**: Ensure Android SDK is properly configured

### Debug Tips

- Check Logcat for detailed error messages
- Use network logging to debug API calls
- Test server endpoint with tools like Postman
- Verify device and PC are on same network

## Future Enhancements

- [ ] Support for multiple image formats
- [ ] Batch image processing
- [ ] Image preview before sending
- [ ] Server URL configuration in app
- [ ] Offline mode with local storage
- [ ] Enhanced UI/UX design

## License

This project is for educational/development purposes.
